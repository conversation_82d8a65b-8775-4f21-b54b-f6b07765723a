import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { Channel } from '../../entities';
import { ChannelService } from './channel.service';

@Auth()
@Crud({
  model: {
    type: Channel,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    /*    join: {
      colorCombinations: { eager: false },
    }, */
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('client/channels')
@Controller('client/channels')
export class ClientChannelController implements CrudController<Channel> {
  constructor(public service: ChannelService) {}
}
