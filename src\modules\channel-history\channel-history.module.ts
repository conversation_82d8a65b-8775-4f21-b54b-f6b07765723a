import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ChannelHistory } from '../../entities';
import { ChannelHistoryService } from './channel-history.service';
import { AdminChannelHistoryController } from './channel-history.controller.admin';
// import { ClientChannelHistoryController } from './channel-history.controller.client';

@Module({
  imports: [TypeOrmModule.forFeature([ChannelHistory])],
  providers: [ChannelHistoryService],
  controllers: [AdminChannelHistoryController],
  exports: [ChannelHistoryService],
})
export class ChannelHistoryModule {}
