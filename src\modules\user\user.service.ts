import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CrudRequest } from '@nestjsx/crud';
import { DeepPartial } from 'typeorm';
import { EncryptionUtil } from '../../common/utils/encryption-hashing.util';
import { UserEntity } from '../../entities';
import { AdminChangePassword } from './user.controller.admin';
import { ITypeOrmCrudService } from '../../common/ITypeOrmCrudService';

@Injectable()
export class UserService extends ITypeOrmCrudService<UserEntity> {
  constructor(@InjectRepository(UserEntity) repo) {
    super(repo);
  }

  async createOne(req: CrudRequest, dto: DeepPartial<UserEntity>) {
    const password = EncryptionUtil.generateStrongPassword();
    dto.password = EncryptionUtil.generateHash(password);
    const user = await super.createOne(req, dto);
    user['rawPassword'] = password;
    return user;
  }

  async changePassword(userId: string, payload: AdminChangePassword) {
    const user = await this.findOneOrNotFound(userId);
    user.password = EncryptionUtil.generateHash(payload.newPassword);
    return this.repo.save(user);
  }

  async deleteBulk(ids: string[]) {
    await this.repo.delete(ids);
    return { message: 'success' };
  }
}
