import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { Audit } from '../../entities';
import { AuditService } from './audit.service';
import { ROLE } from '../../common/enum/role.enum';

@Auth(ROLE.ADMIN)
@Crud({
  model: {
    type: Audit,
  },
  params: {
    id: {
      field: 'id',
      type: 'string',
      primary: true,
    },
  },
  query: {
    alwaysPaginate: true,
    sort: [{ field: 'createdAt', order: 'DESC' }],
    softDelete: true,
    maxLimit: 100,
  },
  routes: {
    only: ['getOneBase', 'getManyBase'],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/audits')
@Controller('admin/audits')
export class AdminAuditController implements CrudController<Audit> {
  constructor(public service: AuditService) {}
}
