import { InjectQueue } from '@nestjs/bullmq';
import { Body, Controller, Param, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Queue } from 'bullmq';
import { AuthApiKey } from '../../../decorators';
import { UniqueIdsDto } from '../../../common/dto/unique-ids.dto';
import { ATGenAuthorCollectorService } from './at-gen-video.service.collector';
import { SourceKind } from '../../../entities';

@AuthApiKey('API_KEY_COLLECTOR')
@ApiTags('collector')
@Controller('collector/at-gen-authors')
export class CollectorATGenVideoAuthorController {
  constructor(
    @InjectQueue('author') private authorQueue: Queue,
    public service: ATGenAuthorCollectorService,
  ) {}

  @Post()
  async handlerCollectorBulk(@Body() payload: UniqueIdsDto) {
    const jobName = 'handleCollector';
    await this.authorQueue.addBulk(
      payload.uniqueIds.map((uniqueId) => ({
        name: jobName,
        data: { uniqueId, sourceKind: SourceKind.ATGenVideo },
        opts: { jobId: `author-${uniqueId}` },
      })),
    );
    return { message: 'job enqueue' };
  }

  @Post(':uniqueId')
  async handlerCollector(@Param('uniqueId') uniqueId: string) {
    return this.service.fetchAllVideoByChannelId(uniqueId);
  }
}
