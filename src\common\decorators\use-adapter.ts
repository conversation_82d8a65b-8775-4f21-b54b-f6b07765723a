import { Redis, Cluster } from 'ioredis';

let sharedRedisClient: Redis | Cluster | null = null;

/**
 * Injects a Redis instance to be used globally by decorators or other utilities.
 * @param client - The Redis or Cluster instance to set.
 */
export const setRedisClient = (client: Redis | Cluster): void => {
  sharedRedisClient = client;
};

/**
 * Retrieves the globally set Redis instance.
 * @returns The Redis or Cluster instance, or null if not set.
 */
export const getRedisClient = (): Redis | Cluster | null => {
  if (!sharedRedisClient) {
    throw new Error(
      'Redis client is not initialized. Please call setRedisClient first.',
    );
  }
  return sharedRedisClient;
};
