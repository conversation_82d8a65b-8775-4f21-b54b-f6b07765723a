import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Audit } from '../../entities';
import { AdminAuditController } from './audit.controller.admin';
import { AuditService } from './audit.service';

@Module({
  imports: [TypeOrmModule.forFeature([Audit])],
  providers: [AuditService],
  controllers: [AdminAuditController],
})
export class AuditModule {}
