import { google } from 'googleapis';
import { drive_v3 } from 'googleapis/build/src/apis/drive/v3';
import { PassThrough } from 'stream';
import { writeToStream } from 'fast-csv';

/**
 * Uploads large CSV data to Google Drive using a streaming writer.
 * @param data - Array of objects to write as rows.
 * @param fileName - File name on Drive.
 * @param parentFolderId - Optional parent folder ID.
 * @param clientId - OAuth2 client ID.
 * @param clientSecret - OAuth2 client secret.
 * @param refreshToken - OAuth2 refresh token.
 * @param makePublic - If true, file is public-readable.
 * @param columnMap - Array of `{ key, header }` for column order and label.
 */
export async function uploadLargeCsvToDrive(
  data: Record<string, any>[],
  fileName: string,
  parentFolderId: string | null,
  clientId: string,
  clientSecret: string,
  refreshToken: string,
  makePublic: boolean = false,
  columnMap?: { key: string; header: string }[],
): Promise<drive_v3.Schema$File> {
  const oauth2Client = new google.auth.OAuth2(clientId, clientSecret);
  oauth2Client.setCredentials({ refresh_token: refreshToken });
  const drive = google.drive({ version: 'v3', auth: oauth2Client });

  const csvStream = new PassThrough();

  const headers = columnMap?.map((col) => col.header);
  const keys = columnMap?.map((col) => col.key);

  const rows = columnMap
    ? data.map((row) => {
        const ordered: Record<string, any> = {};
        columnMap.forEach((col) => {
          ordered[col.header] = row[col.key] ?? '';
        });
        return ordered;
      })
    : data;

  // Stream CSV rows
  setImmediate(() => {
    writeToStream(csvStream, rows, {
      headers: headers ?? true,
      writeHeaders: true,
    }).on('finish', () => {
      csvStream.end();
    });
  });

  const metadata: drive_v3.Schema$File = {
    name: fileName,
    mimeType: 'text/csv',
    ...(parentFolderId && { parents: [parentFolderId] }),
  };

  const media = {
    mimeType: 'text/csv',
    body: csvStream,
  };

  const fileRes = await drive.files.create({
    requestBody: metadata,
    media,
    supportsAllDrives: true,
    fields: 'id, name, webViewLink, webContentLink',
  });

  const fileId = fileRes.data.id!;

  if (makePublic) {
    await drive.permissions.create({
      fileId,
      requestBody: {
        role: 'reader',
        type: 'anyone',
      },
    });
  }

  const finalFile = await drive.files.get({
    fileId,
    fields: 'id, name, webViewLink, webContentLink',
  });

  console.log('✅ CSV uploaded:', finalFile.data.webViewLink);
  return finalFile.data;
}
