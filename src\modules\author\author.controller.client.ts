import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { AuthA<PERSON><PERSON>ey } from '../../decorators';
import { Author } from '../../entities';
import { AuthorService } from './author.service';
import { AssignChannel } from './request/assign-author.dto';
import { ClientPauseAction } from './request/pause-author.dto';
import { Status } from '../../common/enum/status.enum';
import { ClientBulkStatusAction } from './request/status-author.dto';
import { ReassignChannel } from './request/reassign-author.dto';
import { AssignAccount } from './request/assign-account.dto';

@AuthApiKey()
@Crud({
  model: {
    type: Author,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    join: {
      videos: { eager: false },
    },
  },
  routes: {
    only: ['getManyBase'],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('client/authors')
@Controller('client/authors')
export class ClientAuthorController implements CrudController<Author> {
  constructor(public service: AuthorService) {}

  @Patch('reassign-tiktok-died/:profileId')
  async reassignTiktokDied(@Param('profileId') profileId: string) {
    return this.service.reassignTiktokDied(profileId);
  }

  @Post('assign')
  async assignChannel(
    @Body(
      new ValidationPipe({
        skipMissingProperties: false,
      }),
    )
    payload: AssignChannel,
  ) {
    return this.service.assignChannel(payload);
  }

  @Patch('bulk-status')
  async modifyBulkStatus(
    @Body(
      new ValidationPipe({
        skipMissingProperties: false,
      }),
    )
    payload: ClientBulkStatusAction,
  ) {
    return this.service.bulkModifyStatus(
      payload.status as unknown as
        | Status.Active
        | Status.Copyright
        | Status.ProfileDied
        | Status.TiktokDied
        | Status.Inactive,
      payload.profileIds,
    );
  }

  @Patch(':profileId/status')
  async modifyStatus(
    @Param('profileId') profileId: string,
    @Body(
      new ValidationPipe({
        skipMissingProperties: false,
      }),
    )
    payload: ClientPauseAction,
  ) {
    return this.service.toggleAuthorStatus(
      profileId,
      payload.status as unknown as Status.Active | Status.Pause,
    );
  }

  @Get('stats')
  getAuthorStatistical() {
    return this.service.getAuthorStatistical();
  }

  @Patch('assign')
  async updateProfileChannel(
    @Body(
      new ValidationPipe({
        skipMissingProperties: false,
      }),
    )
    payload: ReassignChannel,
  ) {
    return this.service.updateProfileChannel(payload);
  }

  @Patch('account')
  async updateAccountId(
    @Body(
      new ValidationPipe({
        skipMissingProperties: false,
      }),
    )
    payload: AssignAccount,
  ) {
    return this.service.updateAccountIdByEmail(
      payload.email,
      payload.accountId,
      payload.parentId,
    );
  }
}
