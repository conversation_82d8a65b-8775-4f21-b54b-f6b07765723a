import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { Repository } from 'typeorm';
import { Author } from '../../../entities';
import { ATGenVideoService } from '../../../integrations/at-gen-video/at-gen-video.service';
import { VideoService } from '../../video/video.service';
import { Status } from '../../../common/enum/status.enum';

@Injectable()
export class ATGenAuthorCollectorService extends TypeOrmCrudService<Author> {
  constructor(
    @InjectRepository(Author) public readonly repo: Repository<Author>,
    private readonly atGenVideoService: ATGenVideoService,
    private readonly videoService: VideoService,
  ) {
    super(repo);
  }

  fetchNewAuthor() {
    return this.atGenVideoService.fetchNewAuthor();
  }

  async updateAuthorStats(authorId: string) {
    await this.repo
      .createQueryBuilder()
      .update()
      .set({
        lastFetchedVideoId: () =>
          `(SELECT MAX(v.source_id::BIGINT)
            FROM video v
            WHERE v.author_id = :authorId AND v.deleted_at IS NULL)`,
        totalVideo: () =>
          `(SELECT COUNT(*)
            FROM video v
            WHERE v.author_id = :authorId AND v.deleted_at IS NULL)`,
      })
      .where('id = :authorId', { authorId })
      .execute();
  }

  async fetchAllVideoByChannelId(uniqueId: string, stopId = '') {
    const minQualified = 10;
    if (!stopId) {
      const isQualified = await this.isAuthorQualified(uniqueId, minQualified);
      !isQualified && this.throwBadRequestException('Channel not Qualified');
    }

    const bulk = await this.atGenVideoService.fetchAllVideos(uniqueId, stopId);

    for (const item of bulk) {
      await this.videoService.handleCollector(
        item.data.map((video) => video.toCollectorVideo()),
      );
    }

    const author = await this.repo.findOne({ uniqueId });
    await this.updateAuthorStats(author.id);
    if (!stopId) {
      await this.repo.update(author.id, {
        language: 'en',
        isQualified: true,
        status: Status.Active,
      });

      await this.atGenVideoService.assignReup(author.id, uniqueId);
    }

    await author.reload();
    return { author };
  }

  async isAuthorQualified(uniqueId: string, amountVideo?: number) {
    return this.atGenVideoService.isAuthorQualified(uniqueId, amountVideo);
  }
}
