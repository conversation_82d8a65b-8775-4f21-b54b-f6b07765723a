events {}

http {

    resolver 127.0.0.11 valid=10s;  # Docker DNS with re-query every 10 seconds
    client_max_body_size 50M;

    upstream backend_servers {
        # Automatically discover backend containers by service name
        # <PERSON><PERSON>'s internal DNS will resolve 'backend' to all replicas
        server backend:3210;
    }

    server {
        listen 80;

        location / {
            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}

stream {
    server {
      listen 5432;
      proxy_connect_timeout 60s;
      proxy_socket_keepalive on;
      proxy_pass postgres:5432;
    }
}