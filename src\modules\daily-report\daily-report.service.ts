import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { DailyTraceSummaryView } from '../../entities/view';
import { <PERSON>ron } from '@nestjs/schedule';
import { sendMessageViaToken } from '../integrate/lark';
import { CacheTtlSeconds, RedisCacheService } from '../cache/cache.service';
@Injectable()
export class DailyReportService extends TypeOrmCrudService<DailyTraceSummaryView> {
  private readonly logger = new Logger(DailyReportService.name);
  private readonly LARK_BOT_TOKEN_DAILY_REPORT =
    process.env.LARK_BOT_TOKEN_DAILY_REPORT;
  private REPORT_LOCK_KEY = 'report_lock_key';

  constructor(
    @InjectRepository(DailyTraceSummaryView) repo,
    private readonly redisService: RedisCacheService,
  ) {
    super(repo);
  }

  @Cron('0 0 0 * * *') // Run at 00:00:00 UTC every day (7:00:00 AM VN time)
  async sendDailyLarkNotification() {
    const redisInstance = this.redisService.getRedisInstance();
    const lock = await redisInstance.set(
      this.REPORT_LOCK_KEY,
      'locked',
      'EX',
      CacheTtlSeconds.ONE_MINUTE,
      'NX',
    );
    if (!lock) {
      this.logger.warn('Report is running, wait...');
      return;
    }

    this.logger.log('Sending daily notification to Lark...');
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const prevDate = yesterday.toISOString().slice(0, 10); // "YYYY-MM-DD"
    try {
      await this.getDailyReport(prevDate);
      this.logger.log('Notification sent to Lark successfully.');
    } catch (error) {
      this.logger.error('Failed to send notification to Lark', error);
    }
  }

  async getDailyReport(date: string) {
    // Query 1
    const profileStatsQuery = `
        SELECT
          COUNT(*) AS total_profile,
          COUNT(*) FILTER (WHERE a.status = 'active') AS total_profile_active,
          COUNT(DISTINCT parent_id) AS total_parent,
          COUNT(*) FILTER (
            WHERE a.status = 'active' AND daa.status = 'inactive'
          ) AS total_not_upload_video
        FROM author a
        LEFT JOIN daily_absent_author daa ON a.id = daa.author_id
          AND daa."date" = $1
        WHERE a.profile_id IS NOT NULL AND a.parent_id IS NOT NULL
      `;
    const [profileStats] = await this.repo.query(profileStatsQuery, [date]);

    // Query 2
    const lapQuery = `
        SELECT total_lap
        FROM round_lap_tracking
        WHERE "date" = $1 AND flow_name = 'parent-child'
      `;
    const [lapStats] = await this.repo.query(lapQuery, [date]);
    // Tracking lap flow parent-child
    const redisInstance = this.redisService.getRedisInstance();

    const totalIdleParent = await redisInstance.llen(
      // TaskChildService.ACTIVE_LIST_NAME,
      'tc:parents',
    ); // 1 if key exists, 0 if it's gone

    const totalParentRunning = await redisInstance.scard(
      // TaskChildService.LOCK_SET_NAME,
      'tc:lock:parents',
    );

    // Format message for Lark
    const message = [
      `[Flow parent-child] Daily Tracking Report`,
      ` Date: ${date}`,
      `   - Parent: ${profileStats.total_parent}`,
      `   - Total profile: ${profileStats.total_profile}`,
      `   - Profile active: ${profileStats.total_profile_active}`,
      `   - Profile not upload video: ${profileStats.total_not_upload_video} (${
        profileStats.total_profile_active
          ? (
              (profileStats.total_not_upload_video /
                profileStats.total_profile_active) *
              100
            ).toFixed(2)
          : ''
      }%)`,
      `   - Total Lap: ${lapStats?.total_lap ?? 0}`,
      `   - Total Parent Running (tc:lock:parents): ${totalParentRunning}`,
      `   - Total Parent Idle (tc:parents): ${totalIdleParent}`,
    ].join('\n');

    // Send to Lark
    await sendMessageViaToken(message, this.LARK_BOT_TOKEN_DAILY_REPORT);
  }
}
