import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { sleep } from '../../../common/utils';
import { SourceKind } from '../../../entities';
import { ATGenAuthorCollectorService } from '../at-gen-video/at-gen-video.service.collector';
import { AuthorCollectorService } from '../author.service.collector';

const CONCURRENCY = parseInt(process.env.WORKER_CONCURRENCY) || 10;

@Processor('new-video', { concurrency: CONCURRENCY })
export class NewVideoProcessor extends WorkerHost {
  private logger = new Logger(NewVideoProcessor.name);

  constructor(
    public authorCollectorService: AuthorCollectorService,
    public atGenAuthorCollectorService: ATGenAuthorCollectorService,
  ) {
    super();
  }

  async process(
    job: Job<{
      uniqueId: string;
      lastFetchedVideoId: string;
      sourceKind: SourceKind;
    }>,
    token?: string,
  ): Promise<any> {
    const { uniqueId: authorId, lastFetchedVideoId, sourceKind } = job.data;
    this.logger.debug(
      `Start: ${authorId}, lastFetchedVideoId: ${lastFetchedVideoId}, Kind: ${sourceKind}`,
    );

    if (sourceKind == SourceKind.ATGenVideo) {
      await this.atGenAuthorCollectorService.fetchAllVideoByChannelId(
        authorId,
        lastFetchedVideoId,
      );
    } else {
      for await (const [
        err,
        count,
      ] of this.authorCollectorService.handleCollectorIterator(
        authorId,
        lastFetchedVideoId,
      )) {
        if (err) {
          await job.log('Author existed');
          await sleep(1_000);
          return;
        }
        await job.log(`count: ${count}`);
      }
    }

    this.logger.log(`Done ${authorId}`);
  }
}
