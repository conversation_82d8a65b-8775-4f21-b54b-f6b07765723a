import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { C<PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { Channel } from '../../entities';
import { ChannelService } from './channel.service';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

@Auth()
@Crud({
  model: {
    type: Channel,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/channels')
@Controller('admin/channels')
export class AdminChannelController implements CrudController<Channel> {
  constructor(
    public service: ChannelService,
    @InjectQueue('channel') private channelQueue: Queue,
  ) {}

  // @Get('test')
  // test() {
  //   // return this.service.fetchNewChannelId();

  //   // this.channelQueue.add('fetch-channel', {
  //   //   channelId: 'UCvvFrFkk8r8UGB-uxxz1BWA',
  //   // });
  //   return { success: 'ok' };
  // }
}
