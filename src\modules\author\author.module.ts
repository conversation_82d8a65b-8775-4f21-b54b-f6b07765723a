import { <PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { BullModule, InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { noop } from 'rxjs';
import { Author, DailyAbsentAuthor, Video } from '../../entities';
import { IntegrationsModule } from '../../integrations/integrations.module';
import { VideoService } from '../video/video.service';
import { AdminAuthorController } from './author.controller.admin';
import { ClientAuthorController } from './author.controller.client';
import { CollectorAuthorController } from './author.controller.collector';
import { AuthorService } from './author.service';
import { AuthorCollectorService } from './author.service.collector';
import { CollectorATGenVideoAuthorController } from './at-gen-video/at-gen-video.controller.collector';
import { ATGenAuthorCollectorService } from './at-gen-video/at-gen-video.service.collector';
import { ProfileAuthor } from '../../entities';
import { DailyAbsentAuthorService } from '../daily-absent-author/daily-absent-author.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Author, Video, ProfileAuthor, DailyAbsentAuthor]),
    IntegrationsModule,
    BullModule.registerQueue(
      {
        name: 'author',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: 100,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 5_000,
          },
        },
      },
      {
        name: 'new-video',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: 100,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 5_000,
          },
        },
      },
      {
        name: 'cronjob',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: 100,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 5_000,
          },
        },
      },
      {
        name: 'author-event',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: 100,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 5_000,
          },
        },
      },
    ),
  ],
  providers: [
    AuthorService,
    VideoService,
    AuthorCollectorService,
    ATGenAuthorCollectorService,
    DailyAbsentAuthorService,
  ],
  controllers: [
    AdminAuthorController,
    ClientAuthorController,
    CollectorAuthorController,
    CollectorATGenVideoAuthorController,
  ],
  exports: [AuthorService, AuthorCollectorService, ATGenAuthorCollectorService],
})
export class AuthorModule implements OnModuleInit {
  constructor(@InjectQueue('cronjob') private cronjobQueue: Queue) {}

  onModuleInit() {
    Promise.all([
      this.cronjobQueue.upsertJobScheduler(
        'fetch-new-video',
        {
          pattern: '0 0 17 * * *',
        },
        {
          name: 'fetch-new-video',
          data: {},
        },
      ),
      this.cronjobQueue.upsertJobScheduler(
        'fetch-channel-stats',
        {
          // pattern: '*/30 * * * * *',
          pattern: '0 0 17 * * *',
        },
        {
          name: 'fetch-channel-stats',
          data: {},
        },
      ),
      this.cronjobQueue.upsertJobScheduler(
        'track-daily-round',
        {
          pattern: '0 0 17 * * *',
        },
        {
          name: 'track-daily-round',
          data: {},
        },
      ),
      this.cronjobQueue.upsertJobScheduler(
        'fetch-new-author-at-gen-video',
        {
          pattern: '0 0 * * * *',
        },
        {
          name: 'fetch-new-author-at-gen-video',
          data: {},
        },
      ),
      this.cronjobQueue.upsertJobScheduler(
        'daily-absent-author',
        {
          pattern: '0 0 17 * * *',
        },
        {
          name: 'daily-absent-author',
          data: {},
        },
      ),
    ])

      .then(noop)
      .catch(console.error);
  }
}
