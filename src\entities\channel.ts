import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { Column, Entity, OneToMany } from 'typeorm';
import { ChannelHistory } from './channel-history';
import { WithIdAndTimestamp } from './withIdAndTimestamp';

@Entity('channel')
export class Channel extends WithIdAndTimestamp {
  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  title: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  description: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  thumbnail: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  avatar: string;

  @ApiProperty()
  @IsString()
  @Column()
  url: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true })
  vanityChannelUrl: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Column({ nullable: true })
  country: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true })
  joinedDate: Date;

  @ApiProperty()
  @IsString()
  @Column({ unique: true })
  ytbChannelId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint' })
  viewCount: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint' })
  subscriberCount: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint' })
  videoCount: string;

  @ApiProperty()
  @IsString({ each: true })
  @IsOptional()
  @Column('text', { nullable: true, array: true })
  tags: string[];

  @OneToMany(() => ChannelHistory, (history) => history.channel)
  histories: ChannelHistory[];

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint', default: 0 })
  viewCountDiff: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint', default: 0 })
  subscriberCountDiff: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint', default: 0 })
  videoCountDiff: string;
}
