import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class NullLastInterceptor implements NestInterceptor {
  intercept(
    context: ExecutionContext,
    next: <PERSON><PERSON><PERSON><PERSON><any>,
  ): Observable<any> | Promise<Observable<any>> {
    const req = context.switchToHttp().getRequest();
    const parsed = req.NESTJSX_PARSED_CRUD_REQUEST_KEY?.parsed;
    if (parsed) {
      for (const sort of parsed.sort) {
        sort.order += ' NULLS LAST';
      }
    }

    return next.handle();
  }
}
