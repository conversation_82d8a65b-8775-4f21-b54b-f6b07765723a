import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, MaxLength } from 'class-validator';
import { Column, Entity } from 'typeorm';
import { WithIdAndTimestamp } from './withIdAndTimestamp';
import { instanceToPlain, Exclude } from 'class-transformer';

@Entity('{{snakeCase name}}')
export class {{pascalCase name}} extends WithIdAndTimestamp {

}