import { Body, Controller, Get, Logger, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { AuthApi<PERSON>ey } from '../../../decorators';

import { AssignVideosDto } from '../request/assign-videos.dto';
import { TaskServiceV2 } from './task.service';

@AuthApiKey()
@ApiTags('client/tasks')
@Controller({ path: 'tasks', version: '2' })
export class ClientTaskControllerV2 {
  private logger = new Logger(ClientTaskControllerV2.name);
  constructor(private readonly taskService: TaskServiceV2) {}

  @Get('author')
  async getNextTaskTest() {
    // return await this.taskService.getNextAuthor();
    return await this.taskService.getNextAuthorWithFlowReassign();
  }

  @Post('videos')
  async confirmAndGetVideos(@Body() payload: AssignVideosDto) {
    return this.taskService.confirmAndGetVideos(payload.authorId);
  }
}
