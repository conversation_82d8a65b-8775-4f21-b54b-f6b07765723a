import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { Column, Entity, ManyToOne, Unique } from 'typeorm';
import { Channel } from './channel';
import { WithIdAndTimestamp } from './withIdAndTimestamp';

@Entity('channel_history')
@Unique(['channelId', 'date'])
export class ChannelHistory extends WithIdAndTimestamp {
  @ApiProperty()
  @IsString()
  @Column({
    type: 'date',
  })
  date: Date;

  @ApiProperty()
  @IsString()
  @Column()
  channelId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint' })
  viewCount: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint' })
  subscriberCount: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint' })
  videoCount: string;

  @ManyToOne(() => Channel, (chanel) => chanel.histories)
  channel: Channel;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint', default: 0 })
  viewCountDiff: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint', default: 0 })
  subscriberCountDiff: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint', default: 0 })
  videoCountDiff: string;
}
