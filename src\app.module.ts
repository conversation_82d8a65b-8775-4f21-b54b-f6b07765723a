import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';
import { join } from 'path';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { AppController } from './app.controller';
import configuration from './config';
import minioConfiguration from './config/minio.config';
import redisConfiguration from './config/redis.config';
import { AuditModule } from './modules/audit/audit.module';
import { AuthModule } from './modules/auth/auth.module';
import { AuthorModule } from './modules/author/author.module';
import { CacheModule } from './modules/cache/cache.module';
import { ProxyModule } from './modules/proxy/proxy.module';
import { SettingModule } from './modules/setting/setting.module';
import { SharedModule } from './modules/shared/shared.module';
import { TaskModule } from './modules/task/task.module';
import { UploadModule } from './modules/upload/upload.module';
import { UserModule } from './modules/user/user.module';
import { VideoModule } from './modules/video/video.module';
// __import_module_here__
import { DailyAbsentAuthorModule } from './modules/daily-absent-author/daily-absent-author.module';
import { AuthorWarningModule } from './modules/author-warning/author-warning.module';
import { RoundLapTrackingModule } from './modules/round-lap-tracking/round-lap-tracking.module';
import { ChannelHistoryModule } from './modules/channel-history/channel-history.module';
import { ChannelModule } from './modules/channel/channel.module';
import { DailyChannelTraceModule } from './modules/daily-channel-trace/daily-channel-trace.module';
import { ShortVideoHistoryModule } from './modules/short-video-history/short-video-history.module';
import { ShortVideoModule } from './modules/short-video/short-video.module';
import { DailyReportModule } from './modules/daily-report/daily-report.module';
import { LapReportModule } from './modules/lap-report/lap-report.module';
@Module({
  imports: [
    EventEmitterModule.forRoot({ wildcard: true }),
    ConfigModule.forRoot({
      load: [configuration, redisConfiguration, minioConfiguration],
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        url: configService.get<string>('database.url'),
        entities: [join(__dirname, '/entities/**', '*.{ts,js}')],
        logging: !!process.env.DB_LOGGING || ['error'],
        synchronize: configService.get<boolean>('database.synDB'),
        namingStrategy: new SnakeNamingStrategy(),
        maxQueryExecutionTime: 1000,
        applicationName: 'reup-api',
        // extra: {
        //   max: 50, // Maximum number of connections
        //   idleTimeoutMillis: 30000, // Close idle connections after 30 seconds
        //   connectionTimeoutMillis: 2000, // Timeout for new connections (2 seconds)
        // },
      }),
      inject: [ConfigService],
    }),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        connection: configService.get('redis'),
      }),
      inject: [ConfigService],
    }),

    AuthModule,
    SharedModule,
    UploadModule,
    CacheModule,
    UserModule,
    SettingModule,
    AuditModule,
    // __append_module_here__
    DailyAbsentAuthorModule,
    AuthorWarningModule,
    RoundLapTrackingModule,
    ShortVideoHistoryModule,
    ShortVideoModule,
    ChannelHistoryModule,
    ChannelModule,
    DailyChannelTraceModule,
    ProxyModule,
    AuthorModule,
    VideoModule,
    TaskModule,
    DailyReportModule,
    LapReportModule,
  ],
  controllers: [AppController],
  providers: [],
})
export class AppModule {}
