import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { C<PERSON>, CrudController } from '@nestjsx/crud';

import { RoundLapTracking } from '../../entities';
import { RoundLapTrackingService } from './round-lap-tracking.service';
import { Auth } from '../../decorators';

@Auth()
@Crud({
  model: {
    type: RoundLapTracking,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    sort: [{ field: 'date', order: 'DESC' }],
  },
  routes: {
    only: ['getManyBase'],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/round-lap-trackings')
@Controller('admin/round-lap-trackings')
export class AdminRoundLapTrackingController
  implements CrudController<RoundLapTracking>
{
  constructor(public service: RoundLapTrackingService) {}
}
