import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { Repository } from 'typeorm';
import { Video, VideoStatus } from '../../entities';
import { VideoCollectorDto } from './request/video-collector.dto';
import { AuthorService } from '../author/author.service';
import { generateSnowflakeId } from '../../common/utils/snowflake.util';
import { CrudRequest } from '@nestjsx/crud';

@Injectable()
export class VideoService extends TypeOrmCrudService<Video> {
  constructor(
    @InjectRepository(Video) public readonly repo: Repository<Video>,
    private authorService: AuthorService,
  ) {
    super(repo);
  }

  async handleCollector(bulk: VideoCollectorDto[]) {
    const authorMap = await this.authorService.upsertAuthors(
      bulk.map((item) => ({ ...item.author })),
    );

    // Step 2: Prepare videos for saving
    const videosToSave = bulk.map((videoData) => {
      const { author, ...rest } = videoData;
      const authorKey = author.sourceId;
      return this.repo.create({
        ...rest,
        id: generateSnowflakeId(),
        authorId: authorMap.get(authorKey).id,
      });
    });

    // Step 3: Save videos in bulk
    const videos: Video[] = [];
    const chunkSize = 500;
    for (let i = 0; i < videosToSave.length; i += chunkSize) {
      const chunk = videosToSave.slice(i, i + chunkSize);
      const insertResult = await this.repo
        .createQueryBuilder()
        .insert()
        .values(chunk)
        .orIgnore()
        .returning('*')
        .execute();
      if (insertResult.raw.length) {
        videos.push(...(insertResult.generatedMaps as Video[]));
      }
    }
    return { message: 'success' };
  }

  async getLastVideoUploaded(req: CrudRequest) {
    const builder = await this.createBuilder(req.parsed, req.options);
    builder.andWhere('Video.status IN (:...statuses)', {
      statuses: [
        VideoStatus.Completed,
        VideoStatus.Error,
        VideoStatus.InProgress,
      ],
    });

    builder
      .groupBy('Video.authorId')
      .select([
        'MAX("Video"."source_id"::int8) as "lastSourceIdUploaded"',
        'Video.authorId as "authorId"',
      ]);

    const data = await builder.getRawMany();
    return { data };
  }
}
