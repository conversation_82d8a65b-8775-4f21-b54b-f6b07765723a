import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { sleep } from '../../common/utils';
import { retry } from '../../common/utils/retry.util';
import { MultipleLoginBaseResponse as BaseRes } from './dto';
import { ChannelResponseDto, MultiChannelInfoDto } from './dto/channel.res.dto';

const MULTI_API_KEY = process.env.MULTI_API_KEY;

@Injectable()
export class MultiLoginIntegrationService {
  private logger = new Logger(MultiLoginIntegrationService.name);

  private readonly baseUrl = 'https://api.goprofilev4.net/api';

  private retryConfig = {
    attempts: 3,
    delayMs: 5_000,
    exponential: true,
  };

  private _getBaseHeader() {
    return {
      baseURL: this.baseUrl,
      headers: {
        'x-api-key': MULTI_API_KEY,
      },
    };
  }

  async fetchChannelIdByProfileIds(
    profileIds: string[],
  ): Promise<ChannelResponseDto[]> {
    const maxRetries = 3;
    const baseDelay = 3_000;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await axios.post<BaseRes<MultiChannelInfoDto>>(
          '/data/profiles',
          {
            ids: profileIds,
          },
          this._getBaseHeader(),
        );
        if (!response.data.success) {
          console.error(response.data);
          throw new Error('fetchChannelIdByProfileIds::Unknown error');
        }

        return response.data.data
          .filter((item) => item?.youtube?.channelId)
          .map((item) => ({
            channelId: item.youtube.channelId,
            profileEmail: item.youtube.email,
            profileId: item.id,
          }));
      } catch (error) {
        console.error(
          `Attempt ${attempt} failed to fetch videos from MultipleProfile: ${error.message}`,
        );

        if (attempt === maxRetries) {
          // If it's the last attempt, throw the error
          throw new Error(
            `Failed to fetch videos from MultipleProfile API after ${maxRetries} attempts. ${JSON.stringify(
              {
                profileIds,
              },
            )}`,
          );
        }

        // Exponential backoff: Wait for 3s, 6s, 9s before the next retry
        const delay = baseDelay * attempt;
        await sleep(delay);
      }
    }
    throw new Error('Method not implement');
  }

  async changeProfileTiktok(profileId: string, uniqueTikTok: string) {
    this.logger.debug(
      `changeProfileTiktok: profileId=${profileId} uniqueId=${uniqueTikTok}`,
    );

    const payload = {
      profileId,
      tiktok: uniqueTikTok,
    };

    await retry(
      async () => {
        const response = await axios.post<BaseRes<unknown>>(
          '/assign/profileTiktok',
          payload,
          this._getBaseHeader(),
        );

        if (!response.data.success) {
          console.error(response.data);
          throw new Error('fetchChannelIdByProfileIds::Unknown error');
        }
        return { message: 'success' };
      },
      this.retryConfig.attempts,
      this.retryConfig.delayMs,
      this.retryConfig.exponential,
    );
  }
}
