import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthorWarning } from '../../entities';
import { AdminAuthorWarningController } from './author-warning.controller.admin';
import { AuthorWarningService } from './author-warning.service';

@Module({
  imports: [TypeOrmModule.forFeature([AuthorWarning])],
  providers: [AuthorWarningService],
  controllers: [AdminAuthorWarningController],
})
export class AuthorWarningModule {}
