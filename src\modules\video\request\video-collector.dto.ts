import { ApiProperty, OmitType, PickType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { Author, Video } from '../../../entities';

export class AuthorDto extends PickType(Author, [
  'sourceId',
  'uniqueId',
  'nickname',
  'sourceKind',
] as const) {}

export class VideoCollectorDto extends OmitType(Video, [
  'updatedAt',
  'createdAt',
  'deletedAt',
  'author',
  'authorId',
  'status',
] as const) {
  @ApiProperty({ type: AuthorDto })
  @Type(() => AuthorDto)
  @IsObject()
  @ValidateNested()
  author: AuthorDto;
}

export class CreateManyVideosDto {
  @ApiProperty({ type: [VideoCollectorDto] })
  @IsArray()
  @ArrayMinSize(1, {
    message: 'The bulk array must contain at least one value.',
  })
  @ArrayMaxSize(1000)
  @ValidateNested({ each: true })
  @Type(() => VideoCollectorDto)
  bulk: VideoCollectorDto[];
}
