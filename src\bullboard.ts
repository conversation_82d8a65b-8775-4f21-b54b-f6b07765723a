import { INestApplication } from '@nestjs/common';

import { createBullBoard } from '@bull-board/api';
import { ExpressAdapter } from '@bull-board/express';

import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ConfigService } from '@nestjs/config';
import { Queue } from 'bullmq';
import expressBasicAuth from 'express-basic-auth';

const ROUTER = '/bull-board';

export function setupBullBoard(app: INestApplication): void {
  const configService = app.get(ConfigService);

  const serverAdapter = new ExpressAdapter();
  serverAdapter.setBasePath(ROUTER);

  const queueNames = [
    'author',
    'new-video',
    'cronjob',
    'channel',
    'short-video',
    'author-event',
  ];

  createBullBoard({
    queues: queueNames.map(
      (q) =>
        new BullMQAdapter(
          new Queue(q, {
            connection: configService.get<any>('redis'),
          }),
        ),
    ),
    serverAdapter,
  });

  app.use(
    ROUTER,
    expressBasicAuth({
      users: {
        user: 'password',
      },
      challenge: true,
    }),
    serverAdapter.getRouter(),
  );
}
