import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import axios from 'axios';
import { Client as MinioClient } from 'minio';

import minioConfigurationNS from '../../../config/minio.config';
import { noop } from 'rxjs';

class TypedClient extends MinioClient {
  getUri(): string {
    let portMapping = '';

    if (
      (this.protocol == 'http:' && this.port != 80) ||
      (this.protocol == 'https:' && this.port != 443)
    ) {
      portMapping = `:${this.port}`;
    }
    return `${this.protocol}//${this.host}${portMapping}`;
  }
}

@Injectable()
export class MinioService {
  private logger = new Logger(MinioService.name);
  private minioClient: TypedClient;
  private internalMinioClient: TypedClient;

  constructor(
    @Inject(minioConfigurationNS.KEY)
    private minioConfig: ConfigType<typeof minioConfigurationNS>,
  ) {
    // Configure external MinIO client
    // if (this.minioConfig.useSSL) {
    //   delete this.minioConfig.port;
    // }
    this.minioClient = new TypedClient(this.minioConfig);

    // Configure internal MinIO client for Docker Compose DNS
    const internalConfig = {
      ...this.minioConfig,
      endPoint: 'minio-lb',
      useSSL: false,
      port: 9000,
    };
    this.internalMinioClient = new TypedClient(internalConfig);
  }

  getFormatVideoName(objectName: string): string {
    return `${objectName}/video`;
  }

  getFormatThumbnailName(objectName: string): string {
    return `${objectName}/thumb`;
  }

  // Get the external MinIO client
  getClient() {
    return this.minioClient;
  }

  // Get the internal MinIO client
  getInternalClient() {
    return this.internalMinioClient;
  }

  // Ensure bucket exists or create it (uses internal client)
  async ensureBucket(
    bucketName: string,
    useInternalClient = false,
  ): Promise<void> {
    const client = useInternalClient
      ? this.internalMinioClient
      : this.minioClient;
    const bucketExists = await client.bucketExists(bucketName);
    if (!bucketExists) {
      await client.makeBucket(bucketName);
      this.setBucketPublic(bucketName).then(noop).catch(noop);
    }
  }

  // Stream to MinIO (uses internal client by default)
  async streamToMinio(
    videoUrl: string,
    bucketName: string,
    objectName: string,
    proxyAgent = null,
    useInternalClient = true,
  ) {
    try {
      // Fetch the video stream via optional proxy
      const response = await axios({
        url: videoUrl,
        method: 'GET',
        responseType: 'stream',
        ...(proxyAgent && { httpsAgent: proxyAgent }),
      });

      const contentLength = response.headers['content-length'];
      const contentType =
        response.headers['content-type'] || 'binary/octet-stream';

      // Use the appropriate MinIO client
      const client = useInternalClient
        ? this.internalMinioClient
        : this.minioClient;

      // Ensure the bucket exists in MinIO
      const bucketExists = await client.bucketExists(bucketName);
      if (!bucketExists) {
        this.logger.log(`Bucket "${bucketName}" does not exist. Creating...`);
        await client.makeBucket(bucketName);
        this.logger.log(`Bucket "${bucketName}" created successfully.`);
      }

      // Upload the video stream to MinIO
      const result = await client.putObject(
        bucketName,
        objectName,
        response.data,
        contentLength ? parseInt(contentLength) : undefined,
        { 'Content-Type': contentType },
      );
      this.logger.log(`Video uploaded successfully to Etag ${result.etag}`);
    } catch (error) {
      this.logger.error('Error streaming to MinIO:', error.message);
      throw error;
    }
  }

  /**
   * Generate a presigned URL to access an object in MinIO
   * @param bucketName - Name of the bucket
   * @param objectName - Name of the object
   * @param expiry - Expiry time in seconds (default: 3600 seconds or 1 hour)
   * @param useInternalClient - Use internal client for URL generation
   * @returns A presigned URL
   */
  async generateAccessLink(
    bucketName: string,
    objectName: string,
    expiry = 3600,
    useInternalClient = false,
  ): Promise<string> {
    const client = useInternalClient
      ? this.internalMinioClient
      : this.minioClient;

    const isPublic = await this.isBucketPublic(bucketName);

    if (isPublic) {
      return `${client.getUri()}/${bucketName}/${objectName}`;
    }
    // Generate the presigned URL
    const presignedUrl = await client.presignedUrl(
      'GET',
      bucketName,
      objectName,
      expiry,
    );

    return presignedUrl;
  }

  async isBucketPublic(bucketName: string): Promise<boolean> {
    return true;
    // try {
    //   const policy = await this.minioClient.getBucketPolicy(bucketName);
    //   console.log(policy);
    //   return policy.includes(
    //     '{"Action":"s3:GetObject","Effect":"Allow","Principal":"*"}',
    //   );
    // } catch (error) {
    //   return false;
    // }
  }

  async setBucketPublic(bucketName: string): Promise<void> {
    try {
      const policy = JSON.stringify({
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Principal: '*',
            Action: ['s3:GetObject'],
            Resource: [`arn:aws:s3:::${bucketName}/*`],
          },
        ],
      });

      // Assuming this.minioClient is your MinIO client
      await this.minioClient.setBucketPolicy(bucketName, policy);
      console.log(`Bucket ${bucketName} is now public.`);
    } catch (err) {
      console.error('Error setting bucket policy:', err);
    }
  }
}
