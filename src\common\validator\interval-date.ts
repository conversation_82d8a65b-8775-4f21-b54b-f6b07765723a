import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';
import { INTERVAL } from '../utils/date.util';

@Injectable()
export class IntervalValidationPipe implements PipeTransform {
  readonly allowedIntervals: INTERVAL[] = [
    'hour',
    'day',
    'week',
    'month',
    'year',
    'auto',
  ];

  transform(value: any) {
    if (!value) {
      return 'hour';
    }
    if (!this.isValidInterval(value)) {
      throw new BadRequestException(
        `${value} is an invalid interval: ${this.allowedIntervals}`,
      );
    }
    return value;
  }

  private isValidInterval(interval: any) {
    return this.allowedIntervals.includes(interval);
  }
}
