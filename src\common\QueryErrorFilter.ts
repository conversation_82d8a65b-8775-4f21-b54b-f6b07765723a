import { ArgumentsHost, Catch, ConflictException } from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';
import { QueryFailedError } from 'typeorm';

@Catch(QueryFailedError)
export class QueryErrorFilter extends BaseExceptionFilter {
  public catch(exception: any, host: ArgumentsHost): any {
    const detail = exception.detail;
    if (typeof detail === 'string' && detail.includes('already exists')) {
      const messageStart = exception.table.split('_').join(' ') + ' with';
      const cex = new ConflictException(
        exception.detail.replace('Key', messageStart),
      );
      return super.catch(cex, host);
    }
    if (typeof detail === 'string') {
      const cex = new ConflictException(exception.detail);
      return super.catch(cex, host);
    }
    return super.catch(exception, host);
  }
}
