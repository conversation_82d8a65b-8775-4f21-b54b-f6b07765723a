import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { DailyTraceSummaryView } from '../../entities/view';

@Injectable()
export class DailySummaryService extends TypeOrmCrudService<DailyTraceSummaryView> {
  constructor(@InjectRepository(DailyTraceSummaryView) repo) {
    super(repo);
  }
}
