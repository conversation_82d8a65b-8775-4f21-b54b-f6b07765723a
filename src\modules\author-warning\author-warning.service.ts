import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { AuthorWarning } from '../../entities';
import { OnEvent } from '@nestjs/event-emitter';
import { AuthorWarningEvent } from './event';
import { Repository } from 'typeorm';
import { generateSnowflakeId } from '../../common/utils/snowflake.util';

@Injectable()
export class AuthorWarningService extends TypeOrmCrudService<AuthorWarning> {
  constructor(
    @InjectRepository(AuthorWarning) repo: Repository<AuthorWarning>,
  ) {
    super(repo);
  }

  @OnEvent(AuthorWarningEvent.NO_VIDEOS_AVAILABLE, { async: true })
  async handleNoVideosAvailable(payload: {
    authorId: string;
    profileId: string;
    uniqueId: string;
  }) {
    await this.repo.insert({
      ...payload,
      id: generateSnowflakeId(),
      reason: 'No Videos Available',
    });
  }
}
