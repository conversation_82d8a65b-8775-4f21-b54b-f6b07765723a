export async function retry<T>(
  fn: () => Promise<T>,
  retries = 3,
  delayMs = 1000,
  exponential = false,
): Promise<T> {
  let attempt = 0;

  while (attempt < retries) {
    try {
      return await fn();
    } catch (error) {
      attempt++;
      // console.error(
      //   `[${new Date().toLocaleString()}] Retry attempt ${attempt} failed:`,
      //   error.message,
      // );

      if (attempt >= retries) {
        throw error;
      }

      const waitTime = exponential
        ? delayMs * Math.pow(2, attempt - 1)
        : delayMs;
      await new Promise((resolve) => setTimeout(resolve, waitTime));
    }
  }

  throw new Error('Unexpected error in retry function');
}
