import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { DailyAbsentAuthor } from '../../entities';
import { DailyAbsentAuthorService } from './daily-absent-author.service';

@Auth()
@Crud({
  model: {
    type: DailyAbsentAuthor,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
  },
  routes: {
    only: ['getOneBase'],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('client/daily-absent-authors')
@Controller('client/daily-absent-authors')
export class ClientDailyAbsentAuthorController
  implements CrudController<DailyAbsentAuthor>
{
  constructor(public service: DailyAbsentAuthorService) {}
}
