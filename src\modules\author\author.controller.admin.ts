import { Controller, Get, Param, Post, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  <PERSON><PERSON>,
  CrudController,
  CrudRequest,
  CrudRequestInterceptor,
  ParsedRequest,
} from '@nestjsx/crud';

import { Auth, Auth<PERSON>pi<PERSON>ey } from '../../decorators';
import { Author } from '../../entities';
import { AuthorService } from './author.service';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

@Auth()
@Crud({
  params: {
    id: {
      field: 'id',
      type: 'string',
      primary: true,
    },
  },
  model: {
    type: Author,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    sort: [
      {
        field: 'createdAt',
        order: 'DESC',
      },
    ],
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
      'createManyBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/authors')
@Controller('admin/authors')
export class AdminAuthorController implements CrudController<Author> {
  constructor(
    public service: AuthorService,
    @InjectQueue('cronjob') private cronjobQueue: Queue,
  ) {}

  @Get('stats')
  getAuthorStatistical() {
    return this.service.getAuthorStatistical();
  }

  @AuthApiKey()
  @Post('syncAll')
  async syncAll() {
    await this.cronjobQueue.add('fetch-channel-stats', {});
    return { message: 'job enqueue' };
  }

  @Post(':oldId/profile/:newId')
  async handleChangeProfile(
    @Param('oldId') oldId: string,
    @Param('newId') newId: string,
  ) {
    return this.service.handleChangeProfile(oldId, newId);
  }
}
