import { applyDecorators, SetMetadata, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiUnauthorizedResponse } from '@nestjs/swagger';
import { RefreshTokenAuthGuard } from '../guards/refresh-auth.guard';

import { RolesGuard } from '../guards/roles.guard';
import { AuthGuard, OptionalJwtAuthGuard } from './../guards/auth.guard';
import { ROLE } from '../common/enum/role.enum';
import { ROLES_KEY } from '../common/constants/role.constant';

export const Public = () => SetMetadata('isPublic', true);

export function Auth(...roles: ROLE[]) {
  if (roles.length) {
    return applyDecorators(
      SetMetadata(ROLES_KEY, roles),
      UseGuards(AuthGuard, RolesGuard),
      ApiBearerAuth(),
      ApiUnauthorizedResponse({ description: 'Unauthorized' }),
    );
  }
  return applyDecorators(
    UseGuards(AuthGuard),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({ description: 'Unauthorized' }),
  );
}

export function AuthRefreshToken() {
  return applyDecorators(
    UseGuards(RefreshTokenAuthGuard),
    ApiBearerAuth(),
    ApiUnauthorizedResponse({ description: 'Unauthorized' }),
  );
}

export function AuthOptional() {
  return applyDecorators(UseGuards(OptionalJwtAuthGuard));
}
