import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator';
import { Column, Entity } from 'typeorm';
import { WithIdAndTimestamp } from './withIdAndTimestamp';

export enum ProxyProtocol {
  HTTP = 'http',
  HTTPS = 'https',
  SOCKS5 = 'socks5',
}

@Entity('proxy')
export class Proxy extends WithIdAndTimestamp {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true })
  host: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  port: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  username: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  password: string;

  @ApiProperty({ enum: ProxyProtocol })
  @IsOptional()
  @IsEnum(ProxyProtocol)
  @Column({ nullable: true, default: ProxyProtocol.HTTP })
  protocol: ProxyProtocol;

  @ApiProperty()
  @IsBoolean()
  @Column({ default: true })
  isActive: boolean;

  // Method to generate the URI for HttpsProxyAgent
  getProxyUri(): string {
    const authPart =
      this.username && this.password
        ? `${encodeURIComponent(this.username)}:${encodeURIComponent(
            this.password,
          )}@`
        : '';

    return `${this.protocol}://${authPart}${encodeURIComponent(this.host)}:${
      this.port
    }`;
  }
}
