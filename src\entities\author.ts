import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Column, Entity, Index, OneToMany } from 'typeorm';
import { RangeDto } from '../common/dto/range.dto';
import { Status } from '../common/enum/status.enum';
import { MatchesDigits } from '../common/validator/matcher-digits.validator';
import { TransformTimestamp } from '../decorators/transform-timestamp.decorator';
import { Video } from './video';
import { WithIdAndTimestamp } from './withIdAndTimestamp';
import { DailyChannelTrace } from './daily-channel-trace';

export enum SourceKind {
  Tiktok = 'tiktok',
  ATGenVideo = 'at-gen-video-ai',
}

@Entity('author')
@Index('idx_author_status', [
  'status',
  'deletedAt',
  'lastAssignedAt',
  'profileId',
])
// CREATE INDEX idx_author_priority_created_at ON author (priority ASC NULLS LAST, created_at DESC NULLS LAST);
@Index('idx_author_priority_created_at', { synchronize: false })
//CREATE INDEX idx_author_priority_lap_count ON author(priority , lap_count ASC NULLS LAST);
@Index('idx_author_priority_lap_count', { synchronize: false })
export class Author extends WithIdAndTimestamp {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Column({ unique: true })
  sourceId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Column()
  nickname: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Column()
  uniqueId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true })
  avatar: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true })
  category: string;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @Column({ nullable: true })
  followerCount: number;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @Column({ nullable: true })
  diggCount: number;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @Column({ nullable: true })
  playCount: number;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  verified: boolean;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  monitor: boolean;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  like: boolean;

  @ApiProperty()
  @IsOptional()
  @Column({ type: 'jsonb', nullable: true })
  social: any;

  @ApiProperty()
  @IsOptional()
  @Column({ type: 'jsonb', nullable: true })
  snapshot: any;

  @ApiProperty()
  @IsOptional()
  @TransformTimestamp()
  @Type(() => Date)
  @Column({ nullable: true })
  lastTime: Date;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  countryCode: string;

  @OneToMany(() => Video, (video) => video.author)
  videos: Video[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Column({ nullable: true })
  profileId: string;

  @ApiProperty()
  @IsEnum(Status)
  @Column({ default: Status.Active })
  status: string;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  @ValidateNested()
  @Type(() => RangeDto)
  @Column({ nullable: true, type: 'jsonb' })
  dailyUploadLimitRange: RangeDto;

  @Column({ nullable: true })
  lastAssignedAt: Date;

  @Column({ nullable: true, default: 1000 })
  priority: number;

  @ApiProperty()
  @Column({ nullable: true })
  lastFetchedVideoId: string; // for crawler

  @ApiProperty()
  @IsString()
  @IsOptional()
  @MatchesDigits()
  @Column({ nullable: true })
  nextFetchVideoId: string; // for client get video start from

  @Column({ nullable: true, default: 0 })
  totalVideo: number;

  @Column({ nullable: true })
  reservedAt: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true })
  language: string;

  @ApiProperty()
  @IsOptional()
  @Column({ default: false })
  isQualified: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Column({ nullable: true })
  ytbChannelId: string;

  @Column({ nullable: true })
  pausedAt: Date; // backup when all system die, use for cover

  @Column({ nullable: true })
  profileEmail: string;

  @Column({ nullable: true, default: 0 })
  lapCount: number; // for v3, round-based

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true, default: SourceKind.Tiktok })
  sourceKind: SourceKind;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Column({ nullable: true })
  accountId: string;

  @OneToMany(() => DailyChannelTrace, (trace) => trace.author)
  dailyChannelTraces: DailyChannelTrace[];

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  parentId: string;
}
