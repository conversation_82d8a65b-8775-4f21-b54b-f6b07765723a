import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { Repository } from 'typeorm';
import { Status } from '../../common/enum/status.enum';
import { Author } from '../../entities';
// import { MySnapTikIntegrationService } from '../../integrations/my-snaptik/my-snaptik.service';
import { VideoService } from '../video/video.service';
import { SnapTikIntegrationService } from '../../integrations/snaptik/snaptik.service';

@Injectable()
export class AuthorCollectorService extends TypeOrmCrudService<Author> {
  constructor(
    @InjectRepository(Author) public readonly repo: Repository<Author>,
    private readonly snapTikService: SnapTikIntegrationService,
    private readonly videoService: VideoService,
  ) {
    super(repo);
  }

  async recheckAuthor(uniqueId: string) {
    const isQualified = await this.isAuthorQualified(uniqueId);

    !isQualified && this.throwBadRequestException('Author not Qualified');

    const bulkVideo = await this.snapTikService.fetchAllVideos(uniqueId);
    for (const item of bulkVideo) {
      await this.videoService.handleCollector(
        item.items.map((x) => x.toCollectorVideo()),
      );
    }

    const author = await this.repo.findOne({ uniqueId });
    await this.updateAuthorStats(author.id);
    await this.repo.update(author.id, { language: 'en' });
    await author.reload();
    return { author };
  }

  async updateAuthorStats(authorId: string) {
    await this.repo
      .createQueryBuilder()
      .update()
      .set({
        lastFetchedVideoId: () =>
          `(SELECT MAX(v.source_id::BIGINT)
            FROM video v
            WHERE v.author_id = :authorId AND v.deleted_at IS NULL)`,
        totalVideo: () =>
          `(SELECT COUNT(*)
            FROM video v
            WHERE v.author_id = :authorId AND v.deleted_at IS NULL)`,
      })
      .where('id = :authorId', { authorId })
      .execute();
  }

  async *handleCollectorIterator(
    uniqueId: string,
    stopId = '',
  ): AsyncGenerator<[boolean, number]> {
    let author = await this.repo.findOne({ uniqueId });
    let count = 0;

    // if (author) {
    //   yield [true, 0];
    //   return;
    // }

    for await (const response of this.snapTikService.fetchAllVideosIterator(
      uniqueId,
      stopId,
    )) {
      await this.videoService.handleCollector(
        response.items.map((x) => x.toCollectorVideo()),
      );
      count += response?.items?.length || 0;
      yield [false, count];
    }

    author = await this.repo.findOne({ uniqueId });
    await this.updateAuthorStats(author.id);
    await author.reload();
    // it new author, will be verify english
    if (!stopId && author.totalVideo >= 100) {
      await this.repo.update(author.id, {
        language: 'en',
        isQualified: true,
        status: Status.Active,
      });
    }
    yield [false, count];
  }

  async isAuthorQualified(uniqueId: string, amountVideo?: number) {
    return this.snapTikService.isAuthorQualified(uniqueId, amountVideo);
  }
}
