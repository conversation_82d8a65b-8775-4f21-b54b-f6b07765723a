export function getCurrentDayRange(): Date[] {
  const now = new Date();
  const [y, m, d] = [now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()];
  return [
    new Date(Date.UTC(y, m, d)),
    new Date(Date.UTC(y, m, d, 23, 59, 59, 999)),
  ];
}

export function getLastDayOfMonth(date, isWithTime = true): Date {
  const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1);
  nextMonth.setDate(0); // Set to the last day of the previous month
  isWithTime && nextMonth.setHours(23, 59, 59, 999);
  return nextMonth;
}

export function getFirstDayOfMonth(date, isWithTime = true): Date {
  const firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
  isWithTime && firstDayOfMonth.setHours(0, 0, 0, 0);
  return firstDayOfMonth;
}

export function getDateNMonthsBefore(n): Date {
  const date = new Date(); // Current date
  date.setMonth(date.getMonth() - n);
  return date;
}

export type INTERVAL = 'hour' | 'day' | 'week' | 'month' | 'year' | 'auto';

export function getDateFormat(interval: INTERVAL): string {
  switch (interval) {
    case 'day':
      return 'YYYY-MM-DD';
    case 'week':
      return 'IYYY-IW';
    case 'month':
      return 'YYYY-MM';
    case 'year':
      return 'YYYY';
    case 'hour':
      return 'YYYY-MM-DD HH24:MI:SS';
    default:
      throw new Error('Invalid interval');
  }
}

/**
 *
 * Example usage:
```JS const year = 2024;
const week = 29;
const dateRange = getDateRangeFromYearAndWeek(year, week);
console.log(`Week ${week} of ${year} starts on ${dateRange.start.toISOString().split('T')[0]} and ends on ${dateRange.end.toISOString().split('T')[0]}`);
"Week 29 of 2024 starts on 2024-07-15 and ends on 2024-07-21"
```
 * @param year number
 * @param week number
 * @returns ```{start: Date, end: Date}```
 */
export function getDateRangeFromYearAndWeek(year: number, week: number) {
  const firstDayOfYear = new Date(year, 0, 1);
  const dayOfWeek = firstDayOfYear.getUTCDay() || 7; // Make Sunday (0) to be 7

  // Calculate the first Monday of the year
  const firstMondayOfYear = new Date(firstDayOfYear);
  firstMondayOfYear.setUTCDate(firstDayOfYear.getUTCDate() + (8 - dayOfWeek));

  // Calculate the start of the week
  const startOfWeek = new Date(firstMondayOfYear);
  startOfWeek.setUTCDate(firstMondayOfYear.getUTCDate() + (week - 1) * 7);

  // Calculate the end of the week (Saturday)
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setUTCDate(startOfWeek.getUTCDate() + 6);

  return {
    start: startOfWeek,
    end: endOfWeek,
  };
}

export function formatDateISOWithOutTime(date: Date): string {
  return date.toISOString().split('T')[0];
}

export function getInterval(startDate: Date, endDate: Date): INTERVAL {
  const diffInMs = endDate.getTime() - startDate.getTime();
  const diffInHours = diffInMs / (1000 * 60 * 60);

  if (diffInHours <= 24) {
    return 'hour';
  }
  if (diffInHours <= 24 * 31) {
    return 'day';
  }
  if (diffInHours <= 24 * 7 * 5) {
    return 'week';
  }
  if (diffInHours <= 24 * 7 * 5 * 12) {
    return 'month';
  }
  return 'year';
}

export function toDate(value: any): Date | null {
  const date = new Date(value);
  return isNaN(date.getTime()) ? null : date;
}

export function isWithinLastNMinutes(date: Date, n: number): boolean {
  const now = new Date();
  const nMinutesAgo = new Date(now.getTime() - n * 60000); // 60000 milliseconds in a minute
  return date > nMinutesAgo && date <= now;
}

export function convertSeconds(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  const hourString = hours > 0 ? `${hours} hour${hours > 1 ? 's' : ''}` : '';
  const minuteString =
    minutes > 0 ? `${minutes} minute${minutes > 1 ? 's' : ''}` : '';
  const secondString =
    remainingSeconds > 0
      ? `${remainingSeconds} second${remainingSeconds > 1 ? 's' : ''}`
      : '';

  if (hours > 0) {
    return `${hourString} : ${minuteString || '0 minute'} ${
      secondString && `: ${secondString}`
    }`;
  } else if (!hours && minutes > 0) {
    return `${minuteString} ${secondString && `: ${secondString}`}`;
  }

  return secondString;
}

export function get17UTCandBefore() {
  // Get current date in UTC
  const now = new Date();

  // Set the time to 17:00 UTC (5 PM)
  const current17UTC = new Date(
    Date.UTC(
      now.getUTCFullYear(),
      now.getUTCMonth(),
      now.getUTCDate(),
      17,
      0,
      0,
    ),
  );

  // Get 17:00 UTC for the previous day
  const previous17UTC = new Date(current17UTC);
  previous17UTC.setUTCDate(current17UTC.getUTCDate() - 1);

  return [previous17UTC, current17UTC];
}

export function getFormattedFileName(): string {
  const today = new Date();
  const day = String(today.getDate()).padStart(2, '0');
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const year = today.getFullYear();

  return `${day}-${month}-${year}-crawl-youtube.xlsx`;
}

export function getVNDayRange() {
  const now = new Date();

  // Vietnam timezone offset (UTC+7)
  const vietnamOffset = 7 * 60 * 60 * 1000;

  // Current time in Vietnam timezone
  const vietnamTimestamp = now.getTime() + vietnamOffset;

  // Create a Vietnam-local Date object
  const vietnamDate = new Date(vietnamTimestamp);

  // Extract Vietnam's "local" date (year, month, day)
  const vietnamYear = vietnamDate.getUTCFullYear();
  const vietnamMonth = vietnamDate.getUTCMonth();
  const vietnamDay = vietnamDate.getUTCDate();

  // Calculate start of the day in Vietnam
  const startOfDayVN = new Date(
    Date.UTC(vietnamYear, vietnamMonth, vietnamDay),
  );

  // Format start of the day to "YYYY-MM-DD" (date only)
  const startOfDayFormatted = startOfDayVN.toISOString().split('T')[0];

  // Convert start of the day back to UTC
  const startOfDayUTC = new Date(startOfDayVN.getTime() - vietnamOffset);

  // Calculate end of the day in Vietnam
  const endOfDayUTC = new Date(
    startOfDayUTC.getTime() + 24 * 60 * 60 * 1000 - 1,
  );

  return {
    startOfDayUTC,
    endOfDayUTC,
    startOfDayFormatted,
  };
}

/**
 *
 * @param dateString  Parse the input date
 * @param daysToSubtract Subtract the specified days
 * @returns Return the date in YYYY-MM-DD format
 */
export function subtractDays(dateString, daysToSubtract) {
  const date = new Date(dateString);
  date.setDate(date.getDate() - daysToSubtract);
  return date.toISOString().split('T')[0];
}
