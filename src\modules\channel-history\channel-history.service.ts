import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { DeepPartial, Repository } from 'typeorm';
import { ChannelHistory } from '../../entities';

@Injectable()
export class ChannelHistoryService extends TypeOrmCrudService<ChannelHistory> {
  constructor(
    @InjectRepository(ChannelHistory) repo: Repository<ChannelHistory>,
  ) {
    super(repo);
  }

  async upsertChannelHistory(dto: DeepPartial<ChannelHistory>) {
    return this.repo.upsert(dto, {
      conflictPaths: ['channelId', 'date'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async updateChannelDiffs(givenDate: string, channelId: string) {
    return this.repo.query(
      `
      UPDATE channel_history ch
      SET
          view_count_diff = COALESCE(ch.view_count, 0) - COALESCE(prev.view_count, 0),
          subscriber_count_diff = COALESCE(ch.subscriber_count, 0) - COALESCE(prev.subscriber_count, 0),
          video_count_diff = COALESCE(ch.video_count, 0) - COALESCE(prev.video_count, 0)
      FROM channel_history prev
      WHERE ch.channel_id = $1
        AND ch.date = $2
        AND prev.channel_id = ch.channel_id
        AND prev.date = (
            SELECT MAX(date) FROM channel_history
            WHERE channel_id = $1 AND date < $2
        );
      `,
      [channelId, givenDate],
    );
  }
}
