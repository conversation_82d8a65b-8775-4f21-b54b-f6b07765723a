import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { ShortVideo } from '../../entities';
import { ShortVideoService } from './short-video.service';

@Auth()
@Crud({
  model: {
    type: ShortVideo,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('client/short-videos')
@Controller('client/short-videos')
export class ClientShortVideoController implements CrudController<ShortVideo> {
  constructor(public service: ShortVideoService) {}
}
