import { Controller, Get, Param, Query, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  Crud,
  CrudController,
  CrudRequest,
  CrudRequestInterceptor,
  ParsedRequest,
} from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { DailyAbsentAuthor } from '../../entities';
import { DailyAbsentAuthorService } from './daily-absent-author.service';

@Auth()
@Crud({
  params: {
    id: {
      field: 'id',
      type: 'string',
      primary: true,
    },
  },
  model: {
    type: DailyAbsentAuthor,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    join: {
      author: { eager: false },
    },
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/daily-absent-authors')
@Controller('admin/daily-absent-authors')
export class AdminDailyAbsentAuthorController
  implements CrudController<DailyAbsentAuthor>
{
  constructor(public service: DailyAbsentAuthorService) {}

  @UseInterceptors(CrudRequestInterceptor)
  @Get('stats-flows')
  async getStatsFlows(@ParsedRequest() req: CrudRequest) {
    return this.service.getStatsFlows(req);
  }
}
