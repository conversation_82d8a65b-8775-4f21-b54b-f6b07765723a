import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ShortVideo } from '../../entities';
import { ShortVideoService } from './short-video.service';
import { AdminShortVideoController } from './short-video.controller.admin';
import { ShortVideoHistoryModule } from '../short-video-history/short-video-history.module';
// import { ClientShortVideoController } from './short-video.controller.client';

@Module({
  imports: [TypeOrmModule.forFeature([ShortVideo]), ShortVideoHistoryModule],
  providers: [ShortVideoService],
  controllers: [AdminShortVideoController],
  exports: [ShortVideoService],
})
export class ShortVideoModule {}
