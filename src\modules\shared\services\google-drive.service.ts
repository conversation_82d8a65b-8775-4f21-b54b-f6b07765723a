import { Injectable } from '@nestjs/common';
import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class DriveService {
  private drive: any;
  private oauth2Client: OAuth2Client;

  constructor() {
    this.oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI,
    );

    this.oauth2Client.setCredentials({
      refresh_token: process.env.GOOGLE_REFRESH_TOKEN,
    });

    this.drive = google.drive({
      version: 'v3',
      auth: this.oauth2Client,
    });
  }

  // Upload file lên Google Drive
  async uploadFile(filePath: string, mimeType: string) {
    const envFolderName =
      process.env.NODE_ENV === 'production' ? 'Prod' : 'Dev';

    const folderId = await this.getOrCreateFolder(envFolderName);

    const fileMetadata = {
      name: path.basename(filePath),
      parents: [folderId],
    };

    const media = {
      mimeType,
      body: fs.createReadStream(filePath),
    };

    try {
      const response = await this.drive.files.create({
        requestBody: fileMetadata,
        media,
        fields: 'id, webViewLink',
      });

      const fileId = response.data.id;

      // Set the file permission to be publicly readable
      await this.drive.permissions.create({
        fileId,
        requestBody: {
          role: 'reader',
          type: 'anyone',
        },
      });

      return {
        id: fileId,
        link: response.data.webViewLink,
      };
    } catch (error) {
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  // Tải xuống file từ Google Drive
  async downloadFile(fileId: string, destPath: string) {
    const dest = fs.createWriteStream(destPath);

    try {
      const response = await this.drive.files.get(
        { fileId, alt: 'media' },
        { responseType: 'stream' },
      );

      await new Promise((resolve, reject) => {
        response.data
          .on('end', () => resolve(true))
          .on('error', (error) => reject(`Failed to download file: ${error}`))
          .pipe(dest);
      });
    } catch (error) {
      throw new Error(error.message);
    }
  }

  // Lấy danh sách file trong một thư mục
  async listFilesInFolder(folderId: string) {
    try {
      const response = await this.drive.files.list({
        q: `'${folderId}' in parents`,
        fields: 'files(id, name, mimeType)',
      });
      return response.data.files;
    } catch (error) {
      throw new Error(`Failed to list files: ${error.message}`);
    }
  }

  // Xóa file khỏi Google Drive
  async deleteFile(fileId: string) {
    try {
      await this.drive.files.delete({ fileId });
      return { success: true };
    } catch (error) {
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  async getOrCreateFolder(folderName: string): Promise<string> {
    const res = await this.drive.files.list({
      q: `mimeType='application/vnd.google-apps.folder' and name='${folderName}'`,
      fields: 'files(id, name)',
      spaces: 'drive',
    });

    const folder = res.data.files?.[0];
    if (folder) {
      return folder.id;
    }

    const folderMetadata = {
      name: folderName,
      mimeType: 'application/vnd.google-apps.folder',
    };

    const createdFolder = await this.drive.files.create({
      resource: folderMetadata,
      fields: 'id',
    });

    return createdFolder.data.id || '';
  }
}
