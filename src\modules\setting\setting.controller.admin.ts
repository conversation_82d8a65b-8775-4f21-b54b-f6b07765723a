import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { Setting } from '../../entities';
import { SettingService } from './setting.service';

@Auth()
@Crud({
  params: {
    id: {
      field: 'id',
      type: 'string',
      primary: true,
    },
  },
  model: {
    type: Setting,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/settings')
@Controller('admin/settings')
export class AdminSettingController implements CrudController<Setting> {
  constructor(public service: SettingService) {}
}
