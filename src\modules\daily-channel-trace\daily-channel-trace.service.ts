import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { DailyChannelTrace } from '../../entities';
import { getCurrentDayRange } from '../../common/utils/date.util';
import { MoreThanOrEqual } from 'typeorm';
import { CrudRequest } from '@nestjsx/crud';

@Injectable()
export class DailyChannelTraceService extends TypeOrmCrudService<DailyChannelTrace> {
  constructor(@InjectRepository(DailyChannelTrace) repo) {
    super(repo);
  }

  async getTodayAllocation(authorId: string) {
    const [startOfDay] = getCurrentDayRange();
    return this.repo.findOne({
      where: {
        authorId,
        date: MoreThanOrEqual(startOfDay),
      },
    });
  }

  async getAllocationByAuthorIdAndDate(
    authorId: string,
    date: string,
  ): Promise<DailyChannelTrace | null> {
    return this.repo.findOne({
      where: {
        authorId,
        date,
      },
    });
  }

  async updateDetailHistory() {
    const query = `
    UPDATE daily_channel_trace dct
    SET successful_count = sub.success_count,
        failed_count = sub.fail_count
    FROM (
        SELECT
            dct.id,
            COALESCE(SUM(CASE WHEN v.status = 'completed' THEN 1 ELSE 0 END), 0) AS success_count,
            COALESCE(SUM(CASE WHEN v.status = 'error' THEN 1 ELSE 0 END), 0) AS fail_count
        FROM daily_channel_trace dct
        CROSS JOIN LATERAL (
            SELECT v.status
            FROM video v
            WHERE v.id = ANY(dct.video_ids::BIGINT[])
        ) v
        WHERE dct.date >= NOW() - INTERVAL '2 days'
        GROUP BY dct.id
    ) AS sub
    WHERE dct.id = sub.id;`;
    return this.repo.query(query);
  }

  async getStatsFlows(req: CrudRequest) {
    const { options, parsed } = req;
    let builder = await this.createBuilder(parsed, options);

    builder = builder.leftJoin(
      'Author',
      'author',
      'author.id = "DailyChannelTrace".author_id',
    );
    builder.groupBy(`"DailyChannelTrace"."date"`);

    builder.select([
      `"DailyChannelTrace"."date"::text AS "date"`,
      `count(*)::int AS "total"`,
      `COUNT(*) FILTER ( WHERE author.parent_id IS NOT NULL)::int AS "parentChild"`,
      `COUNT(*) FILTER ( WHERE author.parent_id IS NULL)::int AS "normal"`,
    ]);
    const data = await builder.getRawMany();
    return { data };
  }
}
