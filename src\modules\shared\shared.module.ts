import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AwsS3Service } from './services/aws-s3.service';
import { GeneratorService } from './services/generator.service';
import { MinioService } from './services/minio.service';
import { ScheduleModule } from '../schedule/schedule.module';

const providers = [AwsS3Service, GeneratorService, MinioService];

@Global()
@Module({
  providers,
  imports: [ConfigModule, ScheduleModule],
  exports: [...providers],
})
export class SharedModule {}
