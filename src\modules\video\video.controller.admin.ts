import { Controller, Get, Post, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  C<PERSON>,
  CrudController,
  CrudRequest,
  CrudRequestInterceptor,
  ParsedRequest,
} from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { Video } from '../../entities';
import { VideoService } from './video.service';

@Auth()
@Crud({
  params: {
    id: {
      field: 'id',
      type: 'string',
      primary: true,
    },
  },
  model: {
    type: Video,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    join: {
      author: { eager: false },
    },
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
      'createManyBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/videos')
@Controller('admin/videos')
export class AdminVideoController implements CrudController<Video> {
  constructor(public service: VideoService) {}

  @UseInterceptors(CrudRequestInterceptor)
  @Get('last-video-uploaded')
  async getLastVideoUploaded(@ParsedRequest() req: CrudRequest) {
    return this.service.getLastVideoUploaded(req);
  }
}
