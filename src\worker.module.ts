import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';
import { join } from 'path';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { AppController } from './app.controller';
import configuration from './config';
import minioConfiguration from './config/minio.config';
import redisConfiguration from './config/redis.config';
import { AuditModule } from './modules/audit/audit.module';
import { AuthModule } from './modules/auth/auth.module';
import { AuthorModule } from './modules/author/author.module';
import { CacheModule } from './modules/cache/cache.module';
import { ProxyModule } from './modules/proxy/proxy.module';
import { SettingModule } from './modules/setting/setting.module';
import { SharedModule } from './modules/shared/shared.module';
import { UploadModule } from './modules/upload/upload.module';
import { UserModule } from './modules/user/user.module';
import { VideoModule } from './modules/video/video.module';
import { AuthorProcessor } from './modules/author/processor/author.processor';
import { NewVideoProcessor } from './modules/author/processor/new-video.processor';
import { CronjobProcessor } from './modules/author/processor/cronjob.processor';
import { DailyChannelTraceModule } from './modules/daily-channel-trace/daily-channel-trace.module';
import { ChannelModule } from './modules/channel/channel.module';
import { ChannelHistoryModule } from './modules/channel-history/channel-history.module';
import { ShortVideoModule } from './modules/short-video/short-video.module';
import { ShortVideoHistoryModule } from './modules/short-video-history/short-video-history.module';
import { ChannelProcessor } from './modules/channel/processor/channel.processor';
import { ShortVideoProcessor } from './modules/short-video/processor/short-video.processor';
import { AuthorEventProcessor } from './modules/author/processor/author.event.processor';
import { RoundLapTrackingModule } from './modules/round-lap-tracking/round-lap-tracking.module';
import { DailyAbsentAuthorModule } from './modules/daily-absent-author/daily-absent-author.module';
import { IntegrationsModule } from './integrations/integrations.module';
// __import_module_here__

@Module({
  imports: [
    EventEmitterModule.forRoot({ wildcard: true }),
    ConfigModule.forRoot({
      load: [configuration, redisConfiguration, minioConfiguration],
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        url: configService.get<string>('database.url'),
        entities: [join(__dirname, '/entities/**', '*.{ts,js}')],
        logging: !!process.env.DB_LOGGING || ['error'],
        synchronize: configService.get<boolean>('database.synDB'),
        namingStrategy: new SnakeNamingStrategy(),
        maxQueryExecutionTime: 1000,
        applicationName: 'reup-worker',
      }),
      inject: [ConfigService],
    }),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        connection: configService.get('redis'),
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue(
      {
        name: 'dlq',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: 100,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 5_000,
          },
        },
      },
      {
        name: 'new-video',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: 100,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 5_000,
          },
        },
      },
      {
        name: 'short-video',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: 100,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 5_000,
          },
        },
      },
      {
        name: 'channel',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: 100,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 5_000,
          },
        },
      },
      {
        name: 'author',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: 100,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 5_000,
          },
        },
      },
    ),

    AuthModule,
    SharedModule,
    UploadModule,
    CacheModule,
    UserModule,
    SettingModule,
    AuditModule,
    // __append_module_here__
    ProxyModule,
    AuthorModule,
    VideoModule,
    AuthorModule,
    DailyChannelTraceModule,
    ChannelModule,
    ChannelHistoryModule,
    ShortVideoModule,
    ShortVideoHistoryModule,
    RoundLapTrackingModule,
    DailyAbsentAuthorModule,
    IntegrationsModule,
  ],
  controllers: [AppController],
  providers: [
    AuthorProcessor,
    AuthorEventProcessor,
    NewVideoProcessor,
    CronjobProcessor,
    ChannelProcessor,
    ShortVideoProcessor,
  ],
})
export class WorkerModule {}
