# Backup your databases

```
docker exec -t your-db-container pg_dumpall -c -U postgres > dump_`date +%Y-%m-%d"_"%H_%M_%S`.sql
```

Using gzip

```
docker exec -t your-db-container pg_dumpall -c -U postgres | gzip > dump_`date +%Y-%m-%d"_"%H_%M_%S`.sql.gz

docker exec -t 6bc78ff9e63e pg_dumpall -c -U postgres | gzip > dump_`date +%Y-%m-%d"_"%H_%M_%S`.sql.gz

gunzip -c /backup.sql | psql -U postgres

```

# Restore your databases

```
docker cp <path_to_backup_file> <container_name>:/backup.sql

docker exec -it <container_name> /bin/bash

psql -U <username> -d <database_name> -f /backup.sql

example:
psql -U postgres -d crawl-youtube-dev -f /backup.sql
```
