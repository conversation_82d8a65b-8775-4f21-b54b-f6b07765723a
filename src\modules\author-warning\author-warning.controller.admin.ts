import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { AuthorWarning } from '../../entities';
import { AuthorWarningService } from './author-warning.service';

@Auth()
@Crud({
  model: {
    type: AuthorWarning,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    /*    join: {
      colorCombinations: { eager: false },
    }, */
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/author-warnings')
@Controller('admin/author-warnings')
export class AdminAuthorWarningController
  implements CrudController<AuthorWarning>
{
  constructor(public service: AuthorWarningService) {}
}
