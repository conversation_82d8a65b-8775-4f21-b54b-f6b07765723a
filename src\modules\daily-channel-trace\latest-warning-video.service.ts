import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { LatestWarningDailyVideoView } from '../../entities/view';

@Injectable()
export class LatestWarningDailyVideoService extends TypeOrmCrudService<LatestWarningDailyVideoView> {
  constructor(@InjectRepository(LatestWarningDailyVideoView) repo) {
    super(repo);
  }
}
