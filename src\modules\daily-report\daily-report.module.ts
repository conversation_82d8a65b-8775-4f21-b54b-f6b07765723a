import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DailyChannelTrace } from '../../entities';
import { DailyTraceSummaryView } from '../../entities/view';
import { LatestWarningDailyVideoView } from '../../entities/view/latest-warning-daily-video.view';
import { DailyReportService } from './daily-report.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DailyChannelTrace,
      DailyTraceSummaryView,
      LatestWarningDailyVideoView,
    ]),
  ],
  providers: [DailyReportService],
  exports: [DailyReportService],
})
export class DailyReportModule {}
