import { Body, Controller, Delete } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { Proxy } from '../../entities';
import { ProxyService } from './proxy.service';
import { IdsDto } from '../../common/dto';

@Auth()
@Crud({
  model: {
    type: Proxy,
  },
  query: {
    alwaysPaginate: true,
    softDelete: false,
    maxLimit: 100,
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
      'createManyBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/proxies')
@Controller('admin/proxies')
export class AdminProxyController implements CrudController<Proxy> {
  constructor(public service: ProxyService) {}

  @Delete('bulk')
  async deleteBulk(@Body() payload: IdsDto) {
    return this.service.deleteBulk(payload.ids);
  }
}
