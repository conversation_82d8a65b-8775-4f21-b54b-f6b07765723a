import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { Proxy } from '../../entities';
import { ProxyService } from './proxy.service';

@Auth()
@Crud({
  model: {
    type: Proxy,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('client/proxies')
@Controller('client/proxies')
export class ClientProxyController implements CrudController<Proxy> {
  constructor(public service: ProxyService) {}
}
