function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

export function convertKeysToCamelCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map((item) => convertKeysToCamelCase(item));
  } else if (obj !== null && typeof obj === 'object') {
    return Object.entries(obj).reduce((acc, [key, value]) => {
      const camelKey = toCamelCase(key);
      acc[camelKey] = convertKeysToCamelCase(value);
      return acc;
    }, {} as any);
  }
  return obj;
}

export function removePrefixAndCamelCase(obj, prefix: string) {
  if (!obj) return obj;
  const renamedObj = {};

  for (const [key, value] of Object.entries(obj)) {
    // Remove the specified prefix
    const newKey = key.startsWith(prefix) ? key.slice(prefix.length) : key;

    // Convert to camelCase
    const camelCaseKey = convertKeysToCamelCase({ [newKey]: value });
    Object.assign(renamedObj, camelCaseKey);
  }

  return renamedObj;
}
