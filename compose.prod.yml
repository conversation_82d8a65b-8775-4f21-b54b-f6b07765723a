name: reup-tiktok

services:
  backend:
    image: reup-tiktok-core
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env
    deploy:
      replicas: 2
      restart_policy:
        condition: any
    healthcheck:
      test: wget --no-verbose --tries=1 --spider http://0.0.0.0:3210/api/v1/health/ping || exit 1
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 1m
    logging:
      driver: 'json-file'
      options:
        max-size: '20m'
        max-file: '3'

  worker:
    image: reup-tiktok-core
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env
    deploy:
      replicas: 1
      restart_policy:
        condition: any
    command: ['node', 'dist/worker.js']
    logging:
      driver: 'json-file'
      options:
        max-size: '20m'
        max-file: '3'

  nginx:
    image: nginx:latest
    container_name: nginx_load_balancer
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx/prod.conf:/etc/nginx/nginx.conf:ro
      - /root/.mycert:/etc/letsencrypt/:ro
    depends_on:
      - backend
    logging:
      driver: 'json-file'
      options:
        max-size: '20m'
        max-file: '3'
    deploy:
      restart_policy:
        condition: any

volumes:
  postgres_data:
