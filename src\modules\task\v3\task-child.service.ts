import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Repository } from 'typeorm';
import { Author } from '../../../entities';
import { CacheTtlSeconds, RedisCacheService } from '../../cache/cache.service';
import { SettingService } from '../../setting/setting.service';
import { RedisLock } from '../../../common/decorators/redis-lock.decorator';
import { sleep } from '../../../common/utils';
import { sendMessage } from '../../integrate/lark';
import { noop } from 'rxjs';
import { RoundLapTrackingService } from '../../round-lap-tracking/round-lap-tracking.service';
import { Status } from '../../../common/enum/status.enum';

const PREFIX = 'tc:';
const PREFIX_LOCK = 'tc:lock:';
const REST_NEW_LAP = 'tc:rest_new_lap';
const PARENTS_STATS = 'tc:parents:stats';
const LOCK_PARENT_MINUTES = 60;
@Injectable()
export class TaskChildService {
  private static readonly ACTIVE_LIST_NAME = `${PREFIX}parents`;
  private static readonly LOCK_SET_NAME = `${PREFIX_LOCK}parents`;
  private static readonly HASH_TIMELINE_NAME = `${PREFIX}timeline`;

  private logger = new Logger(TaskChildService.name);
  constructor(
    @InjectRepository(Author)
    private readonly authorRepository: Repository<Author>,
    private readonly settingService: SettingService,
    private readonly redisService: RedisCacheService,
    private readonly roundLapTrackingService: RoundLapTrackingService,
  ) {}

  async incrementParentUsage(parentId: string) {
    return this.redisService
      .getRedisInstance()
      .hincrby(PARENTS_STATS, parentId, 1);
  }

  async decrementParentUsage(parentId: string) {
    return this.redisService
      .getRedisInstance()
      .hincrby(PARENTS_STATS, parentId, -1);
  }

  async hasReachedLapLimit(): Promise<boolean> {
    const limit =
      await this.settingService.getAuthorRoundBaseV3LimitLapParentChild();
    if (limit < 0) return false;

    const { totalLap } =
      await this.roundLapTrackingService.getTodayTrackingLapFlowParentChild();
    return totalLap >= limit;
  }

  async getNextChild(): Promise<{ author: Author }> {
    const restNewLap = await this.redisService.getValue(REST_NEW_LAP);
    if (restNewLap) {
      this.logger.warn('Rest new lap, wait...');
      throw new NotFoundException('Rest new lap, wait...');
    }

    // Get the next child job from the active queue
    const redisInstance = this.redisService.getRedisInstance();
    const parentId = await redisInstance.lpop(
      TaskChildService.ACTIVE_LIST_NAME,
    );
    if (!parentId) {
      // Check limit lap per date
      if (await this.hasReachedLapLimit()) {
        throw new NotFoundException('Lap limit reached for today');
      }

      // check lock queue is empty
      const lockQueue = await redisInstance.scard(
        TaskChildService.LOCK_SET_NAME,
      );
      if (lockQueue > 0) {
        this.logger.warn(`There are ${lockQueue} parent in lock queue`);
        // trigger flow clean timeline > 30m
        this.logger.warn(`Check timeline hash...`);
        this.cleanTimelineHash(LOCK_PARENT_MINUTES);
        throw new NotFoundException(
          `There are ${lockQueue} parent in lock queue, wait...`,
        );
      }
      // refill next lap
      this.refillNextLap();
      throw new NotFoundException('Refilling next lap, wait...');
    }

    //move parentId to lock queue
    this.logger.debug(`Moved parentId ${parentId} to lock queue`);
    const wasAddedToLock = await redisInstance.sadd(
      TaskChildService.LOCK_SET_NAME,
      parentId,
    );
    if (wasAddedToLock == 0) {
      this.logger.warn(
        `ParentId ${parentId} already in lock queue, retry new parent...`,
      );
      return this.getNextChild();
    }

    await this.incrementParentUsage(parentId);

    // push parentId to timeline hash
    const currentTime = new Date().getTime();
    redisInstance.hset(
      TaskChildService.HASH_TIMELINE_NAME,
      parentId,
      currentTime,
    );
    this.logger.debug(`Added parentId ${parentId} to timeline hash`);

    //fetch child job by parentId
    const childJobQueue = this.getNameChildJobQueue(parentId);

    const childJobCount = await redisInstance.llen(childJobQueue);

    for (let i = 0; i < childJobCount; i++) {
      const authorId = await redisInstance.lpop(childJobQueue);
      const author = await this.authorRepository
        .createQueryBuilder('author')
        .andWhere('id = :authorId', { authorId })
        .select([
          'author.id',
          'author.profileId',
          'author.sourceId',
          'author.nickname',
          'author.uniqueId',
          'author.dailyUploadLimitRange',
          'author.reservedAt',
          'author.sourceKind',
          'author.accountId',
          'author.parentId',
          'author.sourceKind',
          'author.status',
        ])
        .getOne();

      // Last check, make sure the author is still active
      if (author && author.status == Status.Active) {
        this.logger.log(
          `[FLOW PARENT-CHILD] Get author: authorId: ${author.id}, uniqueId: ${author.uniqueId}, profileId: ${author.profileId}`,
        );
        return { author };
      }
    }

    this.logger.warn(
      `No active child job found for parentId ${parentId}, retry new parent...`,
    );

    // clean parentId, because no active child job found
    await this.confirmDoneParent(parentId);

    return this.getNextChild();
  }

  async confirmDoneParent(parentId: string) {
    const redisInstance = this.redisService.getRedisInstance();
    const wasRemovedFromLock = await redisInstance.srem(
      TaskChildService.LOCK_SET_NAME,
      parentId,
    );
    if (wasRemovedFromLock == 0) {
      this.logger.warn(`[Bypass] ParentId ${parentId} not in lock queue`);
      return { message: 'success' };
    }

    await this.decrementParentUsage(parentId);

    // remove parentId from timeline hash
    await redisInstance.hdel(TaskChildService.HASH_TIMELINE_NAME, parentId);

    // check if child job queue is empty
    const childJobQueue = this.getNameChildJobQueue(parentId);
    const childJobCount = await redisInstance.llen(childJobQueue);
    if (childJobCount > 0) {
      this.logger.warn(
        `Child job queue ${childJobQueue} is not empty, refill parent to active queue`,
      );
      await redisInstance.rpush(TaskChildService.ACTIVE_LIST_NAME, parentId);
      this.logger.debug(`Added parentId ${parentId} to active queue`);
    }

    return { message: 'success' };
  }

  private getNameChildJobQueue(parentId: string): string {
    return `${PREFIX}child_jobs:${parentId}`;
  }

  @RedisLock(`${PREFIX_LOCK}child_refilling`, CacheTtlSeconds.ONE_MINUTE)
  private async cleanTimelineHash(thresholdMinutes: number = 30) {
    const redisInstance = this.redisService.getRedisInstance();
    this.logger.warn(`Clean Timeline Hash ...`);

    const keys = await redisInstance.hkeys(TaskChildService.HASH_TIMELINE_NAME);
    if (keys.length > 0) {
      for (const key of keys) {
        const timestamp = await redisInstance.hget(
          TaskChildService.HASH_TIMELINE_NAME,
          key,
        );
        if (timestamp) {
          const currentTime = new Date().getTime();
          const diff = currentTime - parseInt(timestamp);
          if (diff > thresholdMinutes * 60 * 1000) {
            this.logger.debug(`Removing ${key} from timeline hash`);
            await this.confirmDoneParent(key);
            sendMessage(
              `[Flow Mail child-parent] Parent ${key} was removed from the timeline hash after exceeding the ${thresholdMinutes}-minute timeout.`,
            )
              .then(noop)
              .catch(console.error);
            await redisInstance.hdel(TaskChildService.HASH_TIMELINE_NAME, key);
          }
        }
      }
    }
    await sleep(10_000); // wait 10s because many request will trigger this function
    this.logger.debug(`Clean Timeline Hash done`);
  }

  @RedisLock(`${PREFIX_LOCK}child_refilling`, 40 * CacheTtlSeconds.ONE_MINUTE)
  async refillNextLap(): Promise<void> {
    const redisInstance = this.redisService.getRedisInstance();
    this.logger.warn(`Refill NextLap ...`);

    const query = `
            SELECT a.id, a.parent_id as "parentId"
            FROM author a
            LEFT JOIN LATERAL(
            SELECT v.id v_id
                FROM video v
                WHERE v.author_id = a.id
                AND v.status IN('in_progress', 'pending')
                AND COALESCE(a.next_fetch_video_id:: BIGINT, 0:: BIGINT) <= v.source_id:: BIGINT
                AND v.deleted_at IS NULL
                LIMIT 1
        ) v ON true
            WHERE a.profile_id IS NOT NULL
            AND a.status = 'active'
            AND a.parent_id IS NOT NULL
            AND a.deleted_at IS NULL
            AND v.v_id IS NOT NULL`;

    const authors = await this.authorRepository.query(query);

    if (!authors?.length) {
      this.logger.warn(`No authors found to refill, rest 30s before retry`);
      await sleep(30 * 1000); // retry after 30s
      return;
    }

    const authorsGroupedByParentId: Map<string, string[]> = authors.reduce(
      (map, author) => {
        if (!map.has(author.parentId)) {
          map.set(author.parentId, []);
        }
        map.get(author.parentId).push(author.id);
        return map;
      },
      new Map<string, string[]>(),
    );

    this.logger.debug(`Total parent: ${authorsGroupedByParentId.size} `);

    // Loop through each parentId and add to the queue
    for (const [parentId, authorIds] of authorsGroupedByParentId.entries()) {
      const queueName = this.getNameChildJobQueue(parentId);
      await this.redisService.addIdsToList(
        queueName,
        authorIds.map((id) => BigInt(id)),
      );
      this.logger.debug(
        `Added ${authorIds.length} authors to queue ${queueName}`,
      );
    }

    // Set rest time for 30 minutes
    await this.redisService.setValue(
      REST_NEW_LAP,
      'true',
      30 * CacheTtlSeconds.ONE_MINUTE,
    );

    //[Race Condition] Make sure, no request bypass rest time
    await sleep(5 * 1000);

    // Move parentId to active queue
    const parentIds = Array.from(authorsGroupedByParentId.keys());
    await redisInstance.rpush(TaskChildService.ACTIVE_LIST_NAME, ...parentIds);
    this.logger.debug(`Added ${parentIds.length} parentId to active queue`);

    // notify via Lark
    const currentLap =
      await this.roundLapTrackingService.incNewLapFlowParentChild();
    sendMessage(
      `[Flow Mail child-parent] 
      - Date: ${currentLap.date}
      - Lap ${currentLap.lap} 
      - Total Parent: ${parentIds.length}
      - Total Child: ${authors.length}`,
    )
      .then(noop)
      .catch(console.error);

    this.logger.debug(`Refill NextLap done`);
  }
}
