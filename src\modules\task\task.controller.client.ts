import { <PERSON>, Controller, Get, Logger, Param, Patch } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuthApi<PERSON>ey } from '../../decorators';
import { YoutubeMetadata } from './request/youtube-metadata.dto';
import { TaskService } from './task.service';
import { DigitsOnlyPipe } from '../../pipe/digits-only.pipe';

@AuthApiKey()
@ApiTags('client/tasks')
@Controller('tasks')
export class ClientTaskController {
  private logger = new Logger(ClientTaskController.name);
  constructor(private readonly taskService: TaskService) {}

  // @Get('next')
  // async getNextTask() {
  //   const MAX_RETRIES = 3;
  //   let attempt = 0;

  //   while (attempt < MAX_RETRIES) {
  //     try {
  //       return await this.taskService.getNextTask();
  //     } catch (error) {
  //       attempt++;
  //       if (attempt >= MAX_RETRIES) {
  //         throw new ConflictException(
  //           'Max retry attempts reached due to serialization failure',
  //         );
  //       }

  //       if (error.code === '23505') {
  //         const authorId = error?.parameters[1];
  //         this.logger.warn(`Retrying ${authorId}... Attempt ${attempt}`);
  //         if (authorId) {
  //           await this.taskService.updateAuthorLastAssignedAt(authorId);
  //         }
  //         await sleep(1_000);
  //       } else if (error.code === '55P03') {
  //         // Lock timeout
  //         this.logger.error(`Lock timeout occurred. Retrying...`);
  //         await sleep(2000);
  //       } else if (error.code === '40001') {
  //         // Serialization failure
  //         this.logger.warn(
  //           `Serialization failure. Retrying... Attempt ${attempt}`,
  //         );
  //         await sleep(1000 * attempt); // Exponential backoff
  //       } else {
  //         throw error;
  //       }
  //     }
  //   }
  // }

  @Get('/authors/:id')
  async getNextTaskTest(@Param('id', DigitsOnlyPipe) id: string) {
    return await this.taskService.getNextTaskTest(id);
  }

  @Get('/authors/:id/no-limit')
  async getNextTaskTestNoLimit(@Param('id', DigitsOnlyPipe) id: string) {
    return await this.taskService.getNextTaskNoLimit(id);
  }

  @Patch('complete/video/:id')
  async confirmVideoTaskCompletion(
    @Param('id', DigitsOnlyPipe) id: string,
    @Body() payload: YoutubeMetadata,
  ) {
    await this.taskService.confirmVideoTaskCompletion(id, payload);
    return { message: 'Update status success' };
  }

  @Patch('error/video/:id')
  async confirmVideoTaskError(
    @Param('id', DigitsOnlyPipe) id: string,
    @Body() payload: YoutubeMetadata,
  ) {
    await this.taskService.confirmVideoTaskError(id, payload);
    return { message: 'Update status success' };
  }
}
