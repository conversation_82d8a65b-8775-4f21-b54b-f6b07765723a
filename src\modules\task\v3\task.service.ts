import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';

import { EventEmitter2 } from '@nestjs/event-emitter';
import { noop } from 'rxjs';
import { getRandomIntInclusive, sleep } from '../../../common/utils';
import { getVNDayRange } from '../../../common/utils/date.util';
import { generateSnowflakeId } from '../../../common/utils/snowflake.util';
import {
  Author,
  DailyChannelTrace,
  Video,
  VideoStatus,
} from '../../../entities';
import { CacheTtlSeconds, RedisCacheService } from '../../cache/cache.service';
import { DailyChannelTraceService } from '../../daily-channel-trace/daily-channel-trace.service';
import { sendMessage } from '../../integrate/lark';
import { RoundLapTrackingService } from '../../round-lap-tracking/round-lap-tracking.service';
import { SettingService } from '../../setting/setting.service';
import { AuthorWarningEvent } from '../../author-warning/event';
import { Status } from '../../../common/enum/status.enum';

const REST_NEW_LAP = 'tv3:rest_new_lap';

@Injectable()
export class TaskServiceV3 {
  private logger = new Logger(TaskServiceV3.name);

  private ACTIVE_LAP = 'active_lap';
  private NEXT_LAP = 'next_lap';
  private REFILL_LOCK_KEY = 'next_lap_refilling';

  constructor(
    @InjectRepository(Video)
    private readonly videoRepository: Repository<Video>,
    @InjectRepository(Author)
    private readonly authorRepository: Repository<Author>,
    private readonly settingService: SettingService,
    private readonly dailyChannelTraceService: DailyChannelTraceService,
    private readonly redisService: RedisCacheService,
    private readonly roundLapTrackingService: RoundLapTrackingService,
    private eventEmitter: EventEmitter2,
  ) {}

  async hasReachedLapLimit(): Promise<boolean> {
    const limit = await this.settingService.getAuthorRoundBaseV3LimitLap();
    if (limit < 0) return false;

    const { totalLap } =
      await this.roundLapTrackingService.getTodayTrackingLap();
    return totalLap >= limit;
  }

  public async getNextAuthor(): Promise<{ author: Author }> {
    const restNewLap = await this.redisService.getValue(REST_NEW_LAP);
    if (restNewLap) {
      this.logger.warn(`Rest new lap, wait...');`);
      throw new NotFoundException('Rest new lap, wait...');
    }

    const redisInstance = this.redisService.getRedisInstance();
    let authorId = await redisInstance.lpop(this.ACTIVE_LAP);

    if (!authorId) {
      // Check limit lap per date
      if (await this.hasReachedLapLimit()) {
        throw new NotFoundException('Lap limit reached for today');
      }

      const lockKey = 'lap_switch_lock';

      const lock = await redisInstance.set(lockKey, 'locked', 'EX', 5, 'NX'); // Try acquiring lock

      if (lock) {
        this.logger.debug('[New Lap]: Get new value from NEXT_LAP');
        const nextLapSize = await redisInstance.llen(this.NEXT_LAP);
        const activeLapExists = await redisInstance.exists(this.ACTIVE_LAP);
        const refillLock = await redisInstance.exists(this.REFILL_LOCK_KEY);

        if (nextLapSize === 0 || refillLock) {
          this.refillNextLap();
          await redisInstance.del(lockKey);
          throw new NotFoundException('Refill next lap is running');
        }

        if (!activeLapExists) {
          const currentLap = await this.roundLapTrackingService.incNewLap();
          sendMessage(
            `[${currentLap.date}] Lap ${currentLap.lap} | active_lap: ${nextLapSize}`,
          )
            .then(noop)
            .catch(console.error);
          await redisInstance.rename(this.NEXT_LAP, this.ACTIVE_LAP);
        }

        await redisInstance.del(lockKey); // Release lock
      } else {
        // Wait and retry if another process has the lock
        this.logger.debug(
          '[New Lap} Wait and retry if another process has the lock',
        );
        await new Promise((resolve) => setTimeout(resolve, 100));
        return this.getNextAuthor(); // Recursive retry
      }

      // Try fetching again after rename
      authorId = await redisInstance.lpop(this.ACTIVE_LAP);
    }

    const author = await this.authorRepository
      .createQueryBuilder('author')
      .andWhere('id = :authorId', { authorId })
      .select([
        'author.id',
        'author.profileId',
        'author.sourceId',
        'author.nickname',
        'author.uniqueId',
        'author.dailyUploadLimitRange',
        'author.reservedAt',
        'author.sourceKind',
        'author.accountId',
        'author.status',
      ])
      .getOne();

    // Last check, make sure the author is still active
    if (!author || author.status !== Status.Active) {
      this.logger.warn(
        `Author with ID ${authorId} not found or not "active", retrying...`,
      );
      await new Promise((resolve) => setTimeout(resolve, 100));
      return this.getNextAuthor();
    }
    this.logger.log(
      `[FLOW NORMAL] Get author: authorId: ${author.id}, uniqueId: ${author.uniqueId}, profileId: ${author.profileId}`,
    );
    return { author };
  }

  async updateAuthorLastAssignedAt(authorId: any) {
    return this.authorRepository.update(
      { id: authorId },
      { lastAssignedAt: new Date() },
    );
  }

  async refillNextLap(): Promise<void> {
    const redisInstance = this.redisService.getRedisInstance();

    const REFILL_LOCK_KEY_TTL_SECONDS = 40 * 60;

    // Ensure only one process is refilling
    const refillLock = await redisInstance.set(
      this.REFILL_LOCK_KEY,
      'locked',
      'EX',
      REFILL_LOCK_KEY_TTL_SECONDS,
      'NX',
    );
    if (!refillLock) {
      this.logger.warn(`Another process is already refilling`);
      return;
    }
    this.logger.warn(`Refill NextLap ...`);

    try {
      const query = `
      SELECT a.id
      FROM author a
      LEFT JOIN LATERAL (
          SELECT v.id v_id
          FROM video v
          WHERE v.author_id = a.id
            AND v.status IN ('in_progress', 'pending')
            AND COALESCE(a.next_fetch_video_id::BIGINT, 0::BIGINT) <= v.source_id::BIGINT
            AND v.deleted_at IS NULL
          LIMIT 1
      ) v ON true
      WHERE a.profile_id IS NOT NULL
        AND a.parent_id IS NULL
        AND a.status = 'active'
        AND a.deleted_at IS NULL
        AND v.v_id IS NOT NULL`;
      const authorIds = await this.authorRepository.query(query);

      this.logger.debug(`Total author: ${authorIds?.length}`);

      if (!authorIds?.length) {
        this.logger.warn(`No authors found to refill, rest 30s before retry`);
        await sleep(30 * 1000);
        return;
      }

      await this.redisService.setValue(
        REST_NEW_LAP,
        'true',
        30 * CacheTtlSeconds.ONE_MINUTE,
      );

      await sleep(5 * 1000);

      const ids = authorIds.map((x) => x.id);
      await this.redisService.addIdsToList(this.NEXT_LAP, ids);
    } finally {
      await redisInstance.del(this.REFILL_LOCK_KEY); // Release lock
    }
  }

  async confirmAndGetVideos(authorId: string) {
    let videos = [];
    let authorResponse = null;
    let dailyUploadLimitRange = null;
    const { startOfDayFormatted } = getVNDayRange();

    // Check author allocation schedule today
    const allocation =
      await this.dailyChannelTraceService.getAllocationByAuthorIdAndDate(
        authorId,
        startOfDayFormatted,
      );

    this.logger.debug('[confirmAndGetVideos]: new allocation video');
    await this.videoRepository.manager.transaction(
      async (transactionalEntityManager) => {
        const author = await transactionalEntityManager.findOne(Author, {
          where: { id: authorId },
          select: [
            'id',
            'profileId',
            'uniqueId',
            'nickname',
            'sourceId',
            'dailyUploadLimitRange',
            'reservedAt',
            'sourceKind',
            'status',
            'nextFetchVideoId',
          ],
        });

        if (!author) {
          throw new NotFoundException('Author not found');
        }

        if (author.status != Status.Active) {
          throw new ConflictException('Author is not active');
        }

        // Get upload limit range
        dailyUploadLimitRange =
          author.dailyUploadLimitRange ||
          (await this.settingService.getDailyUploadLimitRange());
        const randomRange = getRandomIntInclusive(
          dailyUploadLimitRange.min,
          dailyUploadLimitRange.max,
        );
        const nextFetchVideoId = author.nextFetchVideoId || 0;

        // Fetch videos
        videos = await transactionalEntityManager
          .createQueryBuilder(Video, 'video')
          .where('video.author_id = :authorId', { authorId })
          .andWhere('video.status IN (:...status)', {
            status: [VideoStatus.InProgress, VideoStatus.Pending],
          })
          .andWhere('video.source_id::BIGINT > :nextFetchVideoId', {
            nextFetchVideoId,
          })
          .orderBy('video.source_id::BIGINT')
          .limit(randomRange)
          .select([
            'video.id',
            'video.sourceId',
            'video.description',
            'video.title',
            'video.cover',
            'video.video',
            'video.videoUrl',
            'video.videoDriveUrl',
          ])
          .getMany();

        if (!videos.length) {
          sendMessage(
            [
              `⚠️⚠️⚠️ No Videos Available`,
              '',
              `Author ID: ${author.id}`,
              `Unique ID: ${author.uniqueId}`,
              `Profile ID: ${author.profileId}`,
            ].join('\n'),
          )
            .then(noop)
            .catch(console.error);

          this.eventEmitter.emit(AuthorWarningEvent.NO_VIDEOS_AVAILABLE, {
            authorId: author.id,
            profileId: author.profileId,
            uniqueId: author.uniqueId,
          });

          throw new NotFoundException('No videos available');
        }

        const videoIds = videos.map((v) => v.id);
        const maxSourceId = videos.length
          ? videos.reduce((max, v) =>
              BigInt(v.sourceId) > BigInt(max.sourceId) ? v : max,
            ).sourceId
          : null;

        // Update video status
        await transactionalEntityManager.update(
          Video,
          { id: In(videoIds) },
          { status: VideoStatus.InProgress, assignedAt: new Date() },
        );

        // Create daily trace
        if (!allocation) {
          await transactionalEntityManager.insert(DailyChannelTrace, {
            id: generateSnowflakeId(),
            authorId,
            date: startOfDayFormatted,
            videoIds,
            totalAllocations: videoIds.length,
          });
        } else {
          await transactionalEntityManager
            .createQueryBuilder()
            .update(DailyChannelTrace)
            .set({
              totalAllocations: () => `total_allocations + ${videoIds.length}`,
              videoIds: () =>
                `array_cat(video_ids, ARRAY[:...newVideoIds]::text[])`, // Efficiently appends new IDs
            })
            .where('id = :id', { id: allocation.id })
            .setParameters({ newVideoIds: ['0', ...videoIds] }) // The array of new video IDs to append
            .execute();
        }

        // Clear the reservation (so the author can be assigned again later)

        await transactionalEntityManager
          .createQueryBuilder(Author, 'author')
          .update()
          .where('id = :authorId', { authorId })
          .set({
            reservedAt: null,
            lastAssignedAt: new Date(),
            nextFetchVideoId: maxSourceId.toString(), //next_fetch_video_id is varchar
            lapCount: () => `lap_count + 1`,
          })
          .execute();

        // Return the response structure
        authorResponse = author;
        delete authorResponse.reservedAt;
      },
    );
    const uploadTimeConfig = await this.settingService.getUploadTimeConfig();
    return {
      author: authorResponse,
      videos,
      dailyUploadLimitRange,
      uploadTimeConfig,
    };
  }

  async getNextChild() {}
}
