import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { Column, Entity, Unique } from 'typeorm';
import { WithIdAndTimestamp } from './withIdAndTimestamp';

export enum RoundLapTrackingFlowName {
  NORMAL = 'normal',
  PARENT_CHILD = 'parent-child',
}

@Entity('round_lap_tracking')
@Unique(['date', 'flowName'])
export class RoundLapTracking extends WithIdAndTimestamp {
  @ApiProperty()
  @IsString()
  @Column({
    type: 'date',
  })
  date: Date;

  @Column({ default: 1 })
  totalLap: number;

  @Column({ default: RoundLapTrackingFlowName.NORMAL, nullable: true })
  flowName: string;
}
