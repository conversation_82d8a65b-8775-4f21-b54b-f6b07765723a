import axios from 'axios';

export async function checkResourceStatus(url: string) {
  try {
    const response = await axios.head(url);

    // Check if the status is 200
    if (response.status === 200) {
      console.log(`Resource is available: ${url}`);
      return true;
    } else {
      console.log(`Resource returned status: ${response.status}`);
      return false;
    }
  } catch (error) {
    // Handle error (e.g., 404, network issues, etc.)
    if (error.response) {
      console.error(
        `Resource check failed with status: ${error.response.status}`,
      );
    } else {
      console.error(`Request error: ${error.message}`);
    }
    return false;
  }
}
