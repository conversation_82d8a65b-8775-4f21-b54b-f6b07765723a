// eslint-disable-next-line @typescript-eslint/no-var-requires
const pluralize = require('pluralize');

module.exports = function (plop) {
  plop.setHelper('plural', function (text) {
    return pluralize.plural(text);
  }),
    // controller generator
    plop.setGenerator('crud', {
      description: 'Generate CRUD nestjx ',
      prompts: [
        {
          type: 'input',
          name: 'name',
          message: 'Entity name please',
        },
      ],
      actions: [
        {
          type: 'add',
          path: 'src/modules/{{dashCase name}}/{{dashCase name}}.controller.admin.ts',
          templateFile: 'ducnvx/plop-templates/controller.admin.hbs',
        },
        {
          type: 'add',
          path: 'src/modules/{{dashCase name}}/{{dashCase name}}.controller.client.ts',
          templateFile: 'ducnvx/plop-templates/controller.client.hbs',
        },
        {
          type: 'add',
          path: 'src/modules/{{dashCase name}}/{{dashCase name}}.service.ts',
          templateFile: 'ducnvx/plop-templates/service.hbs',
        },
        {
          type: 'add',
          path: 'src/modules/{{dashCase name}}/{{dashCase name}}.module.ts',
          templateFile: 'ducnvx/plop-templates/module.hbs',
        },
        {
          type: 'append',
          path: 'src/app.module.ts',
          pattern: '__append_module_here__',
          template: '\t\t{{pascalCase name}}Module,',
        },
        {
          type: 'append',
          path: 'src/app.module.ts',
          pattern: '__import_module_here__',
          template: `import { {{pascalCase name}}Module } from "./modules/{{dashCase name}}/{{dashCase name}}.module";`,
        },
        {
          type: 'add',
          path: 'src/entities/{{dashCase name}}.ts',
          templateFile: 'ducnvx/plop-templates/entity.hbs',
        },
        {
          type: 'append',
          path: 'src/entities/index.ts',
          pattern: '__entity_here__',
          template: `export * from "./{{dashCase name}}";`,
        },
      ],
    });
};
