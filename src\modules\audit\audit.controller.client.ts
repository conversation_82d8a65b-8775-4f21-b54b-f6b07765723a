import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { Audit } from '../../entities';
import { AuditService } from './audit.service';

@Auth()
@Crud({
  model: {
    type: Audit,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    /*    join: {
      colorCombinations: { eager: false },
    }, */
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('client/audits')
@Controller('client/audits')
export class ClientAuditController implements CrudController<Audit> {
  constructor(public service: AuditService) {}
}
