import { NotFoundException } from '@nestjs/common';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

export class ITypeOrmCrudService<T> extends TypeOrmCrudService<T> {
  protected getSelect(query, options): string[] {
    return [...new Set(super.getSelect(query, options))];
  }

  async findOneOrNotFound(id: unknown): Promise<T> {
    try {
      return await this.repo
        .createQueryBuilder(this.alias)
        .andWhere('id = :id', { id })
        .getOne();
    } catch (error) {
      throw new NotFoundException(`${this.alias} with id ${id} not found.`);
    }
  }
}
