import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsString } from 'class-validator';
import { Column, Entity, Unique } from 'typeorm';
import { WithIdAndTimestamp } from './withIdAndTimestamp';

@Entity('setting')
@Unique(['key', 'groupName'])
export class Setting extends WithIdAndTimestamp {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Column()
  key: string;

  @ApiProperty()
  @IsObject()
  @Column({ type: 'jsonb' })
  value: any;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  description: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Column()
  groupName: string;
}
