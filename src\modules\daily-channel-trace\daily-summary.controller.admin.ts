import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { DailyTraceSummaryView } from '../../entities/view';
import { DailySummaryService } from './daily-summary.service';

@Auth()
@Crud({
  model: {
    type: DailyTraceSummaryView,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    join: {
      author: { eager: false },
    },
    sort: [
      {
        field: 'date',
        order: 'DESC',
      },
    ],
  },
  routes: {
    only: ['getManyBase'],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/daily-summary')
@Controller('admin/daily-summary')
export class AdminDailySummaryController
  implements CrudController<DailyTraceSummaryView>
{
  constructor(public service: DailySummaryService) {}
}
