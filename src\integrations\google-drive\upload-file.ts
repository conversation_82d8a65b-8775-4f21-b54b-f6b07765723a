import fs from 'fs';
import { google } from 'googleapis';
import { drive_v3 } from 'googleapis/build/src/apis/drive/v3';

/**
 * Uploads a file to Google Drive and optionally makes it publicly viewable.
 * @param filePath - Local path to the file.
 * @param fileName - File name to be used in Google Drive.
 * @param parentFolderId - Optional Drive folder ID to upload into.
 * @param clientId - Google OAuth2 client ID.
 * @param clientSecret - Google OAuth2 client secret.
 * @param refreshToken - OAuth2 refresh token.
 * @param makePublic - If true, sets the file to be public-readable.
 * @returns Metadata about the uploaded file.
 */
export async function uploadFileToDrive(
  filePath: string,
  fileName: string,
  parentFolderId: string | null,
  clientId: string,
  clientSecret: string,
  refreshToken: string,
  makePublic: boolean = false,
): Promise<drive_v3.Schema$File> {
  const oauth2Client = new google.auth.OAuth2(clientId, clientSecret);
  oauth2Client.setCredentials({ refresh_token: refreshToken });

  const drive = google.drive({ version: 'v3', auth: oauth2Client });

  const fileMetadata: drive_v3.Schema$File = {
    name: fileName,
    ...(parentFolderId && { parents: [parentFolderId] }),
  };

  const media = {
    mimeType: 'application/octet-stream',
    body: fs.createReadStream(filePath),
  };

  const createRes = await drive.files.create({
    requestBody: fileMetadata,
    media,
    supportsAllDrives: true,
    fields: 'id, name, webViewLink, webContentLink',
  });

  const fileId = createRes.data.id!;

  if (makePublic) {
    await drive.permissions.create({
      fileId,
      requestBody: {
        role: 'reader',
        type: 'anyone',
      },
    });
  }

  const file = await drive.files.get({
    fileId,
    fields: 'id, name, webViewLink, webContentLink',
  });

  console.log('✅ Uploaded to Google Drive:', file.data);
  return file.data;
}
