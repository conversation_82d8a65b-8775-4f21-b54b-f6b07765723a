import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { ShortVideo } from '../../entities';
import { DeepPartial, Repository } from 'typeorm';
import { generateSnowflakeId } from '../../common/utils/snowflake.util';
import { getVNDayRange } from '../../common/utils/date.util';
import { ShortVideoHistoryService } from '../short-video-history/short-video-history.service';

@Injectable()
export class ShortVideoService extends TypeOrmCrudService<ShortVideo> {
  constructor(
    @InjectRepository(ShortVideo) repo: Repository<ShortVideo>,
    private shortVideoHistoryService: ShortVideoHistoryService,
  ) {
    super(repo);
  }

  async upsertShortVideo(bulk: DeepPartial<ShortVideo>[]) {
    bulk.forEach((video) => (video.id = generateSnowflakeId()));

    const insertResult = await this.repo
      .createQueryBuilder('sv')
      .insert()
      .values(bulk)
      .orUpdate(['view_count', 'like_count'], ['video_id'])
      .returning(['videoId', 'id'])
      .execute();

    const mapShortVideo = new Map(
      insertResult.generatedMaps.map((video) => [video.videoId, video.id]),
    );

    const { startOfDayFormatted } = getVNDayRange();
    await this.shortVideoHistoryService.upsertShortVideoHistory(
      bulk.map((item) => ({
        id: generateSnowflakeId(),
        likeCount: item.likeCount,
        viewCount: item.viewCount,
        date: startOfDayFormatted,
        shortVideoId: mapShortVideo.get(item.videoId),
      })),
    );

    const shortVideoIds = Array.from(mapShortVideo.values());

    await this.updateChannelDiffs(startOfDayFormatted, shortVideoIds);

    await this.shortVideoHistoryService.updateChannelDiffs(
      startOfDayFormatted,
      shortVideoIds,
    );
    return { message: 'success' };
  }

  async updateChannelDiffs(givenDate: string, shortVideoIds: string[]) {
    return this.repo.query(
      `
      UPDATE short_video sv
      SET
          view_count_diff = COALESCE(sv.view_count, 0) - COALESCE(svh.view_count, 0),
          like_count_diff = COALESCE(sv.like_count, 0) - COALESCE(svh.like_count, 0)
      FROM short_video_history svh
      INNER JOIN (
          SELECT short_video_id, MAX(date) as latest_date
          FROM short_video_history
          WHERE short_video_id = ANY($1) AND date < $2
          GROUP BY short_video_id
      ) latest ON svh.short_video_id = latest.short_video_id AND svh.date = latest.latest_date
      WHERE sv.id = svh.short_video_id AND sv.id = ANY($1);
      `,
      [shortVideoIds, givenDate],
    );
  }
}
