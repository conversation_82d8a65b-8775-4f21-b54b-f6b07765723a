import {
  IsString,
  IsN<PERSON>ber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { VideoCollectorDto } from '../../modules/video/request/video-collector.dto';

class AuthorDto {
  @IsString()
  @Expose()
  id: string;

  @IsString()
  @Expose()
  uniqueId: string;

  @IsString()
  @Expose()
  nickname: string;

  @IsString()
  @Expose()
  avatar: string;
}

class StatsDto {
  @IsNumber()
  @Expose()
  playCount: number;

  @IsNumber()
  @Expose()
  shareCount: number;

  @IsNumber()
  @Expose()
  downloadCount: number;
}

export class VideoDto {
  @ValidateNested()
  @Expose()
  @Type(() => AuthorDto)
  @Expose()
  author: AuthorDto;

  @IsString()
  @Expose()
  videoId: string;

  @IsString()
  @Expose()
  awemeId: string;

  @IsString()
  @Expose()
  cover: string;

  @IsNumber()
  @Expose()
  videoDuration: number;

  @IsNumber()
  @Expose()
  videoSize: number;

  @ValidateNested()
  @Expose()
  @Type(() => StatsDto)
  @Expose()
  stats: StatsDto;

  @IsOptional()
  @Expose()
  @IsString()
  @Expose()
  musicUrl: string;

  @IsString()
  @Expose()
  title: string;

  @IsOptional()
  @Expose()
  @IsString()
  @Expose()
  videoUrl: string;

  toCollectorVideo(): VideoCollectorDto {
    return {
      author: {
        sourceId: this.author.id,
        uniqueId: this.author.uniqueId,
        nickname: this.author.nickname,
      },
      sourceId: this.videoId,
      cover: this.cover,
      // videoDuration: this.videoDuration,
      // videoSize: this.videoSize,
      playCount: this.stats.playCount,
      shareCount: this.stats.shareCount,
      // downloadCount: this.stats.downloadCount,
      // musicUrl: this.musicUrl,
      description: this.title,
      // videoUrl: this.videoUrl,
    } as VideoCollectorDto;
  }
}

export class SnapTikResponse {
  @Expose() @Type(() => VideoDto) items: VideoDto[];
  @Expose() userId: string;
  @Expose() credit: string;
  @Expose() statusCode: string;
  @Expose() cursor: string;
  @Expose() hasMore: boolean;
}
