import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>Array,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { VideoCollectorDto } from '../../../modules/video/request/video-collector.dto';
import { SourceKind } from '../../../entities';

class ChannelDto {
  @ApiProperty({ example: '30657754553123840' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ example: 'zkd3s31329_01' })
  @IsString()
  @IsNotEmpty()
  uniqueId: string;

  @ApiProperty({ example: 'zkd3s31329_01' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ example: null, nullable: true })
  @IsOptional()
  @IsString()
  description: string | null;
}

class SubTaskDto {
  @ApiProperty({ example: '31222320894837760' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ example: 'Take Action NOW: Short Motivation #2' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: null, nullable: true })
  @IsOptional()
  @IsString()
  videoScript: string | null;

  @ApiProperty({ example: ['action', 'growth', 'journey', 'change', 'moment'] })
  @IsArray()
  @IsString({ each: true })
  searchTerms: string[];

  @ApiProperty({
    example:
      'https://s3-store-video-ai.atechdigital.space:6869/gen-video/video.mp4',
  })
  @IsString()
  @IsNotEmpty()
  videoUrl: string;

  @ApiProperty({ nullable: true })
  @IsString()
  @IsOptional()
  videoDriveUrl: string | null;
}

export class ATVideos {
  @ApiProperty({ example: '31229894818006016' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ example: '30657754553123840' })
  @IsString()
  @IsNotEmpty()
  channelId: string;

  @ApiProperty({ type: ChannelDto })
  @ValidateNested()
  @Type(() => ChannelDto)
  channel: ChannelDto;

  @ApiProperty({ type: SubTaskDto })
  @ValidateNested()
  @Type(() => SubTaskDto)
  subTask: SubTaskDto;

  toCollectorVideo(): VideoCollectorDto {
    return {
      author: {
        sourceId: this.channel.id,
        uniqueId: this.channel.id,
        nickname: this.channel.title,
        sourceKind: SourceKind.ATGenVideo,
      },
      sourceId: this.id,
      videoUrl: this.subTask.videoUrl,
      videoDriveUrl: this.subTask.videoDriveUrl,
      // cover: "",
      // videoDuration: this.videoDuration,
      // videoSize: this.videoSize,
      // playCount: this.stats.playCount,
      // shareCount: this.stats.shareCount,
      // downloadCount: this.stats.downloadCount,
      // musicUrl: this.musicUrl,
      description: this.subTask.videoScript,
      title: this.subTask.name,
      // videoUrl: this.videoUrl,
    } as VideoCollectorDto;
  }
}
