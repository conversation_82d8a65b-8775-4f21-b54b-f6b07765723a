import { InjectQueue } from '@nestjs/bullmq';
import { Body, Controller, Param, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Queue } from 'bullmq';
import { UniqueIdsDto } from '../../common/dto/unique-ids.dto';
import { Auth<PERSON><PERSON><PERSON>ey } from '../../decorators';
import { AuthorCollectorService } from './author.service.collector';
import { SourceKind } from '../../entities';

@AuthApiKey('API_KEY_COLLECTOR')
@ApiTags('collector')
@Controller('collector/authors')
export class CollectorAuthorController {
  constructor(
    @InjectQueue('author') private authorQueue: Queue,
    @InjectQueue('new-video') private newVideoQueue: Queue,
    @InjectQueue('cronjob') private cronjobQueue: Queue,
    public service: AuthorCollectorService,
  ) {}

  @Post('fetch-author-language-all')
  async handlerFetchAuthorLanguageAll() {
    await this.cronjobQueue.add('fetch-language', {});
  }

  @Post('fetch-new-video-all')
  async handlerCollectorFromAuthorTableBatch() {
    const jobName = 'handleCollector';
    const batchSize = 1000; // Adjust batch size as needed
    let offset = 0;

    while (true) {
      // Fetch a batch of authors
      const authors = await this.service.repo.find({
        select: ['uniqueId', 'lastFetchedVideoId'],
        // where: {
        //   status: Status.Active,
        // },
        order: { createdAt: 'ASC' },
        skip: offset,
        take: batchSize,
      });

      if (!authors.length) {
        break; // Exit loop if no more authors
      }

      // Enqueue jobs for the current batch
      await this.newVideoQueue.addBulk(
        authors.map((author) => ({
          name: jobName,
          data: {
            uniqueId: author.uniqueId,
            lastFetchedVideoId: author.lastFetchedVideoId,
          },
          opts: { jobId: `author-${author.uniqueId}` },
        })),
      );

      offset += batchSize;
    }

    return { message: 'Jobs enqueued for all authors in batches.' };
  }

  @Post()
  async handlerCollectorBulk(@Body() payload: UniqueIdsDto) {
    const jobName = 'handleCollector';
    await this.authorQueue.addBulk(
      payload.uniqueIds.map((uniqueId) => ({
        name: jobName,
        data: { uniqueId, sourceKind: SourceKind.Tiktok },
        opts: { jobId: `author-${uniqueId}` },
      })),
    );
    return { message: 'job enqueue' };
  }

  @Post(':uniqueId')
  async handlerCollector(@Param('uniqueId') uniqueId: string) {
    return this.service.recheckAuthor(uniqueId);
  }
}
