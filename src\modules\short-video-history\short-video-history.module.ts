import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ShortVideoHistory } from '../../entities';
import { ShortVideoHistoryService } from './short-video-history.service';
import { AdminShortVideoHistoryController } from './short-video-history.controller.admin';
// import { ClientShortVideoHistoryController } from './short-video-history.controller.client';

@Module({
  imports: [TypeOrmModule.forFeature([ShortVideoHistory])],
  providers: [ShortVideoHistoryService],
  controllers: [AdminShortVideoHistoryController],
  exports: [ShortVideoHistoryService],
})
export class ShortVideoHistoryModule {}
