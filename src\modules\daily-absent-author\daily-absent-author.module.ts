import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DailyAbsentAuthor } from '../../entities';
import { AdminDailyAbsentAuthorController } from './daily-absent-author.controller.admin';
import { DailyAbsentAuthorService } from './daily-absent-author.service';

@Module({
  imports: [TypeOrmModule.forFeature([DailyAbsentAuthor])],
  providers: [DailyAbsentAuthorService],
  controllers: [AdminDailyAbsentAuthorController],
  exports: [DailyAbsentAuthorService],
})
export class DailyAbsentAuthorModule {}
