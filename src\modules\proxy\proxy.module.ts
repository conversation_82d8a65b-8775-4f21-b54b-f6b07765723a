import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Proxy } from '../../entities';
import { AdminProxyController } from './proxy.controller.admin';
import { ProxyService } from './proxy.service';

@Module({
  imports: [TypeOrmModule.forFeature([Proxy])],
  providers: [ProxyService],
  controllers: [AdminProxyController],
  exports: [ProxyService],
})
export class ProxyModule {}
