import { Controller, Get, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  <PERSON><PERSON>,
  CrudController,
  CrudRequest,
  CrudRequestInterceptor,
  ParsedRequest,
} from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { DailyChannelTrace } from '../../entities';
import { DailyChannelTraceService } from './daily-channel-trace.service';

@Auth()
@Crud({
  model: {
    type: DailyChannelTrace,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    join: {
      author: { eager: false },
    },
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/daily-channel-traces')
@Controller('admin/daily-channel-traces')
export class AdminDailyChannelTraceController
  implements CrudController<DailyChannelTrace>
{
  constructor(public service: DailyChannelTraceService) {}

  @UseInterceptors(CrudRequestInterceptor)
  @Get('stats-flows')
  async getStatsFlows(@ParsedRequest() req: CrudRequest) {
    return this.service.getStatsFlows(req);
  }
}
