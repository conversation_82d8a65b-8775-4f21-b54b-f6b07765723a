import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, In, Repository } from 'typeorm';

import { Status } from '../../../common/enum/status.enum';
import { getRandomIntInclusive } from '../../../common/utils';
import { getVNDayRange } from '../../../common/utils/date.util';
import { generateSnowflakeId } from '../../../common/utils/snowflake.util';
import { removePrefixAndCamelCase } from '../../../common/utils/string.util';
import {
  Author,
  DailyChannelTrace,
  Video,
  VideoStatus,
} from '../../../entities';
import { SettingService } from '../../setting/setting.service';
import { LatestWarningDailyVideoService } from '../../daily-channel-trace/latest-warning-video.service';
import { DailyChannelTraceService } from '../../daily-channel-trace/daily-channel-trace.service';
import { LatestWarningDailyVideoView } from '../../../entities/view';

@Injectable()
export class TaskServiceV2 {
  private logger = new Logger(TaskServiceV2.name);

  constructor(
    @InjectRepository(Video)
    private readonly videoRepository: Repository<Video>,
    @InjectRepository(Author)
    private readonly authorRepository: Repository<Author>,
    private readonly settingService: SettingService,
    private readonly dailyChannelTraceService: DailyChannelTraceService,
    private readonly latestWarningDailyVideoService: LatestWarningDailyVideoService,
  ) {}

  async updateAuthorLastAssignedAt(authorId: any) {
    return this.authorRepository.update(
      { id: authorId },
      { lastAssignedAt: new Date() },
    );
  }

  async getNextAuthorWithFlowReassign() {
    const author = await this.getNextAuthor();
    this.logger.debug('[getNextAuthor]: New author');
    return author;
  }

  async getNextAuthor(): Promise<{ author: Author }> {
    const { startOfDayUTC: startOfDay } = getVNDayRange();

    this.logger.debug('[getNextAuthor]: New author');
    const eligibleVideoStatuses = [VideoStatus.InProgress, VideoStatus.Pending];

    let author = null;
    await this.videoRepository.manager.transaction(
      async (transactionalEntityManager) => {
        const cooldownMinutes =
          await this.settingService.getAuthorCooldownPeriod();

        const queryBuilder = await transactionalEntityManager
          .createQueryBuilder(Author, 'author')
          .innerJoin(
            Video,
            'video',
            'author.id = video.authorId AND video.status IN (:...status)' +
              ' AND COALESCE("author"."next_fetch_video_id"::BIGINT, 0::BIGINT) <= "video"."source_id"::BIGINT',
            { status: eligibleVideoStatuses },
          )
          .select([
            'author.id',
            'author.profileId',
            'author.sourceId',
            'author.nickname',
            'author.uniqueId',
            'author.dailyUploadLimitRange',
            'author.reservedAt',
            'author.accountId',
          ])
          .andWhere(
            new Brackets((qb) => {
              qb.orWhere('author.lastAssignedAt IS NULL');
              qb.orWhere('author.lastAssignedAt < :startOfDay', { startOfDay });
            }),
          )
          .andWhere('author.profileId IS NOT NULL')
          .andWhere('author.status = :authorStatus', {
            authorStatus: Status.Active,
          })
          .andWhere(
            new Brackets((qb) => {
              qb.orWhere('author.reservedAt IS NULL');
              qb.orWhere(
                `author.reservedAt < NOW() - INTERVAL '${cooldownMinutes} MINUTE'`,
              );
            }),
          )
          .orderBy('author.priority', 'ASC', 'NULLS LAST')
          .addOrderBy('author.createdAt', 'DESC', 'NULLS LAST')
          .limit(1);

        const sql = queryBuilder.getSql() + ' FOR UPDATE SKIP LOCKED';
        const parameters = [
          ...eligibleVideoStatuses,
          startOfDay,
          Status.Active,
        ];

        const rawResults = await transactionalEntityManager.query(
          sql,
          parameters,
        );
        author = removePrefixAndCamelCase(rawResults[0], 'author_') as Author;

        if (!author) {
          throw new NotFoundException('No authors available');
        }

        // Mark the author as reserved
        await transactionalEntityManager.update(
          Author,
          { id: author.id },
          { reservedAt: new Date() },
        );
      },
    );

    return { author };
  }

  async confirmAndGetVideos(authorId: string) {
    let videos = [];
    let authorResponse = null;
    let dailyUploadLimitRange = null;
    const { startOfDayFormatted } = getVNDayRange();

    // Check author allocation schedule today
    const allocation =
      await this.dailyChannelTraceService.getAllocationByAuthorIdAndDate(
        authorId,
        startOfDayFormatted,
      );

    if (allocation) {
      this.logger.debug('[confirmAndGetVideos]: reallocate video');
      return this.reallocateVideo(allocation);
    }

    this.logger.debug('[confirmAndGetVideos]: new allocation video');
    await this.videoRepository.manager.transaction(
      async (transactionalEntityManager) => {
        const author = await transactionalEntityManager.findOne(Author, {
          where: { id: authorId },
          select: [
            'id',
            'profileId',
            'uniqueId',
            'nickname',
            'sourceId',
            'dailyUploadLimitRange',
            'reservedAt',
            'status',
            'nextFetchVideoId',
          ],
        });

        if (!author) {
          throw new NotFoundException('Author not found');
        }

        if (author.status != Status.Active) {
          throw new ConflictException('Author is not active');
        }

        // Ensure author is still reserved
        const cooldownMinutes =
          await this.settingService.getAuthorCooldownPeriod();
        const authorCooldownPeriod = new Date(
          Date.now() - cooldownMinutes * 60 * 1000,
        );
        if (!author.reservedAt || author.reservedAt < authorCooldownPeriod) {
          throw new BadRequestException('Author reservation expired');
        }

        // Get upload limit range
        dailyUploadLimitRange =
          author.dailyUploadLimitRange ||
          (await this.settingService.getDailyUploadLimitRange());
        const randomRange = getRandomIntInclusive(
          dailyUploadLimitRange.min,
          dailyUploadLimitRange.max,
        );
        const nextFetchVideoId = author.nextFetchVideoId || 0;

        // Fetch videos
        videos = await transactionalEntityManager
          .createQueryBuilder(Video, 'video')
          .where('video.author_id = :authorId', { authorId })
          .andWhere('video.status IN (:...status)', {
            status: [VideoStatus.InProgress, VideoStatus.Pending],
          })
          .andWhere('video.source_id::BIGINT > :nextFetchVideoId', {
            nextFetchVideoId,
          })
          .orderBy('video.source_id::BIGINT')
          .limit(randomRange)
          .select([
            'video.id',
            'video.sourceId',
            'video.description',
            'video.title',
            'video.cover',
            'video.video',
          ])
          .getMany();

        if (!videos.length) {
          throw new NotFoundException('No videos available');
        }

        const videoIds = videos.map((v) => v.id);

        // Update video status
        await transactionalEntityManager.update(
          Video,
          { id: In(videoIds) },
          { status: VideoStatus.InProgress, assignedAt: new Date() },
        );

        // Create daily trace
        await transactionalEntityManager.insert(DailyChannelTrace, {
          id: generateSnowflakeId(),
          authorId,
          date: startOfDayFormatted,
          videoIds,
          totalAllocations: videoIds.length,
        });

        // Clear the reservation (so the author can be assigned again later)
        await transactionalEntityManager.update(
          Author,
          { id: authorId },
          { reservedAt: null, lastAssignedAt: new Date() },
        );

        // Return the response structure
        authorResponse = author;
        delete authorResponse.reservedAt;
      },
    );
    const uploadTimeConfig = await this.settingService.getUploadTimeConfig();
    return {
      author: authorResponse,
      videos,
      dailyUploadLimitRange,
      uploadTimeConfig,
    };
  }

  async fetchPriorityAuthor(
    startOfDayFormatted: string,
  ): Promise<{ author: Author } | null> {
    let author = null;

    await this.videoRepository.manager.transaction(
      async (transactionalEntityManager) => {
        const queryBuilder = await transactionalEntityManager
          .createQueryBuilder(LatestWarningDailyVideoView, 'lv')
          .where('lv.date = :date', { date: startOfDayFormatted })
          .limit(1);

        const sql = queryBuilder.getSql() + ' FOR UPDATE SKIP LOCKED';
        const rawResults = await transactionalEntityManager.query(sql, [
          startOfDayFormatted,
        ]);

        const latest = removePrefixAndCamelCase(
          rawResults[0],
          'lv_',
        ) as LatestWarningDailyVideoView;

        if (!latest) {
          return null;
        }

        author = await transactionalEntityManager
          .createQueryBuilder(Author, 'author')
          .select([
            'author.id',
            'author.profileId',
            'author.sourceId',
            'author.nickname',
            'author.uniqueId',
            'author.dailyUploadLimitRange',
            'author.reservedAt',
          ])
          .where('id = :authorId', { authorId: latest.authorId })
          .getOne();

        const now = new Date();

        await transactionalEntityManager
          .createQueryBuilder(DailyChannelTrace, 'lv')
          .update()
          .set({
            reassignHistory: () =>
              `array_append(reassign_history, '${now.toISOString()}')`,
            reservedAt: now,
          })
          .where('id = :id', { id: latest.id })
          .execute();
      },
    );

    return author && { author };
  }

  async reallocateVideo(
    allocation: DailyChannelTrace,
  ): Promise<{ profileId: string; videos: Video[] }> {
    // Ensure author is still reserved

    const authorCoolDownPeriod = new Date(Date.now() - 60 * 60 * 1000);

    if (
      !allocation.reservedAt ||
      allocation.reservedAt < authorCoolDownPeriod
    ) {
      throw new BadRequestException('Author reservation expired');
    }

    // Query total error/completed videos for today
    const totalCompleted = await this.videoRepository.count({
      where: {
        id: In(Array.from(new Set(allocation.videoIds))),
        status: VideoStatus.Completed,
      },
    });

    if (totalCompleted >= allocation.totalAllocations) {
      throw new ConflictException(
        'All videos for this author have already been allocated and completed for today.',
      );
    }

    const totalReassign = allocation.totalAllocations - totalCompleted;

    const eligibleVideoStatuses = [VideoStatus.InProgress, VideoStatus.Pending];

    let result = null;
    // Start a transaction and ensure all operations are within this transaction
    await this.videoRepository.manager.transaction(
      async (transactionalEntityManager) => {
        // Step 1: Select a random author
        const author = await transactionalEntityManager
          .createQueryBuilder(Author, 'author')
          .setLock('pessimistic_write')
          .innerJoin(
            Video,
            'video',
            'author.id = video.authorId AND video.status IN(:...status) ',
            { status: eligibleVideoStatuses },
          )
          .select('author.id')
          .addSelect('author.nickname')
          .addSelect('author.uniqueId')
          .addSelect('author.sourceId')
          .addSelect('author.profileId')
          .addSelect('author.nextFetchVideoId')
          .addSelect('author.dailyUploadLimitRange')
          .andWhere('author.id = :id', { id: allocation.authorId })
          .andWhere('author.profileId IS NOT NULL')
          .andWhere('author.status = :authorStatus', {
            authorStatus: Status.Active,
          })
          .orderBy('author.priority', 'ASC', 'NULLS LAST')
          .limit(1)
          .getOne();

        if (!author) {
          throw new NotFoundException('No tasks available');
        }

        await transactionalEntityManager.update(
          Author,
          { id: author.id },
          { lastAssignedAt: new Date() },
        );

        // Step 1.1: get dailyUploadLimitRange
        let dailyUploadLimitRange = author.dailyUploadLimitRange;
        if (!dailyUploadLimitRange) {
          dailyUploadLimitRange =
            await this.settingService.getDailyUploadLimitRange();
        }

        // Step 2: Fetch new videos to reassign
        const nextFetchVideoId = author.nextFetchVideoId || 0;
        const videos = await transactionalEntityManager
          .createQueryBuilder(Video, 'video')
          .setLock('pessimistic_write')
          .where('video.author_id = :authorId', {
            authorId: author.id,
          })
          .andWhere('video.status In (:...status)', {
            status: eligibleVideoStatuses,
          })
          .andWhere('video.source_id::BIGINT >= :nextFetchVideoId', {
            nextFetchVideoId,
          })
          .orderBy('video.source_id::BIGINT')
          .limit(totalReassign)
          .select([
            'video.id',
            'video.sourceId',
            'video.description',
            'video.cover',
            'video.video',
          ])
          .getMany();

        if (!videos) {
          throw new NotFoundException('No tasks available');
        }

        const videoIds = videos.map((video) => video.id);

        await transactionalEntityManager.update(
          Video,
          { id: In(videoIds) },
          { status: VideoStatus.InProgress, assignedAt: new Date() },
        );

        await transactionalEntityManager
          .createQueryBuilder()
          .update(DailyChannelTrace)
          .set({
            videoIds: () =>
              `array_cat(video_ids, ARRAY[:...newVideoIds]::text[])`, // Efficiently appends new IDs
          })
          .where('id = :id', { id: allocation.id })
          .setParameters({ newVideoIds: ['0', ...videoIds] }) // The array of new video IDs to append
          .execute();

        const uploadTimeConfig =
          await this.settingService.getUploadTimeConfig();
        result = { author, videos, dailyUploadLimitRange, uploadTimeConfig };
      },
    );

    return result;
  }
}
