import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Author, Video } from '../../entities';
import { AuthorModule } from '../author/author.module';
import { ProxyModule } from '../proxy/proxy.module';
import { AdminVideoController } from './video.controller.admin';
import { ClientVideoController } from './video.controller.client';
import { VideoService } from './video.service';
import { CollectorVideoController } from './video.controller.collector';

@Module({
  imports: [
    TypeOrmModule.forFeature([Video, Author]),

    AuthorModule,
    ProxyModule,
  ],
  providers: [VideoService],
  controllers: [
    AdminVideoController,
    ClientVideoController,
    CollectorVideoController,
  ],
  exports: [VideoService],
})
export class VideoModule {}
