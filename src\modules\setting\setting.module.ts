import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Setting } from '../../entities';
import { AdminSettingController } from './setting.controller.admin';
import { SettingService } from './setting.service';

@Module({
  imports: [TypeOrmModule.forFeature([Setting])],
  providers: [SettingService],
  controllers: [AdminSettingController],
  exports: [SettingService],
})
export class SettingModule {}
