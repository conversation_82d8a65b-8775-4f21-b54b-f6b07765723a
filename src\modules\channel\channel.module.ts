import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Channel } from '../../entities';
import { ChannelService } from './channel.service';
import { AdminChannelController } from './channel.controller.admin';
import { ChannelHistoryModule } from '../channel-history/channel-history.module';
import { BullModule } from '@nestjs/bullmq';
import { CollectorAuthorController } from './channe.controller.collector';
// import { ClientChannelController } from './channel.controller.client';

@Module({
  imports: [
    TypeOrmModule.forFeature([Channel]),
    ChannelHistoryModule,
    BullModule.registerQueue({
      name: 'channel',
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: 100,
        attempts: 2,
        backoff: {
          type: 'exponential',
          delay: 5_000,
        },
      },
    }),
    BullModule.registerQueue({
      name: 'short-video',
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: 100,
        attempts: 2,
        backoff: {
          type: 'exponential',
          delay: 5_000,
        },
      },
    }),
  ],
  providers: [ChannelService],
  controllers: [AdminChannelController, CollectorAuthorController],
  exports: [ChannelService],
})
export class ChannelModule {}
