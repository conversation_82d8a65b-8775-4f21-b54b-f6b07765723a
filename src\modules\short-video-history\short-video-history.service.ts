import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { ShortVideoHistory } from '../../entities';
import { DeepPartial, Repository } from 'typeorm';

@Injectable()
export class ShortVideoHistoryService extends TypeOrmCrudService<ShortVideoHistory> {
  constructor(
    @InjectRepository(ShortVideoHistory) repo: Repository<ShortVideoHistory>,
  ) {
    super(repo);
  }

  async upsertShortVideoHistory(bulk: DeepPartial<ShortVideoHistory>[]) {
    await this.repo.upsert(bulk, {
      conflictPaths: ['shortVideoId', 'date'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async updateChannelDiffs(givenDate: string, shortVideoIds: string[]) {
    return this.repo.query(
      `
      UPDATE short_video_history svh
      SET
          view_count_diff = COALESCE(svh.view_count, 0) - COALESCE(prev.view_count, 0),
          like_count_diff = COALESCE(svh.like_count, 0) - COALESCE(prev.like_count, 0)
      FROM short_video_history prev
      INNER JOIN (
          SELECT short_video_id, MAX(date) as latest_date
          FROM short_video_history
          WHERE short_video_id = ANY($1) AND date < $2
          GROUP BY short_video_id
      ) latest ON prev.short_video_id = latest.short_video_id AND prev.date = latest.latest_date
      WHERE svh.short_video_id = latest.short_video_id AND svh.date = $2;
      `,
      [shortVideoIds, givenDate],
    );
  }
}
