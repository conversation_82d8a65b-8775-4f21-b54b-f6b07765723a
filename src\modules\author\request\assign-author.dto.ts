import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { SourceKind } from '../../../entities/author';

export class AssignChannel {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  profileId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  @Transform(({ value }) => value?.toUpperCase())
  countryCode?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  accountId: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  parentId?: string;

  @ApiProperty({ enum: SourceKind })
  @IsOptional()
  @IsEnum(SourceKind)
  @IsNotEmpty()
  sourceKind?: SourceKind;
}
