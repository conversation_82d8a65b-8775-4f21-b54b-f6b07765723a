// src/common/guards/api-key.guard.ts
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['x-api-key'];

    const anotherKey = this.reflector.getAllAndOverride<string>(
      '__AuthApiKey__',
      [context.getHandler(), context.getClass()],
    );

    if (!this.isValidApiKey(apiKey, anotherKey)) {
      throw new UnauthorizedException('Invalid API key');
    }

    return true;
  }

  private isValidApiKey(apiKey: string, anotherKey: string): boolean {
    return (
      apiKey === process.env.API_KEY ||
      (anotherKey && process.env[anotherKey] === apiKey)
    );
  }
}
