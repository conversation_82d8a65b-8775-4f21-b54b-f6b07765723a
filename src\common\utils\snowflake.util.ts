import { Snowflake } from 'nodejs-snowflake';

const epoch = new Date('2024-12-31').getTime();
const snowflake = new Snowflake({
  custom_epoch: epoch,
  // instance_id: parseInt(process.env.INSTANCE_ID || '1', 10), [TODO: research instance_id with docker compose]
});

// Function to generate a Snowflake ID
export const generateSnowflakeId = (): string =>
  snowflake.getUniqueID().toString();

export const getInstanceId = (): number => snowflake.instanceID();
