import { Body, Controller, Delete, Param, Patch } from '@nestjs/common';
import { ApiBody, ApiTags, PickType } from '@nestjs/swagger';
import {
  <PERSON><PERSON>,
  CrudController,
  CrudRequest,
  Override,
  ParsedBody,
  ParsedRequest,
} from '@nestjsx/crud';

import { ROLE } from '../../common/enum/role.enum';
import { Auth, AuthUser } from '../../decorators';
import { UserEntity } from '../../entities';
import { ChangePasswordDto } from '../auth/dto/change-password';
import { UserService } from './user.service';
import { IdsDto } from '../../common/dto';

export class AdminChangePassword extends PickType(ChangePasswordDto, [
  'newPassword',
] as const) {}

@Auth(ROLE.ADMIN)
@Crud({
  model: {
    type: UserEntity,
  },
  params: {
    id: {
      field: 'id',
      type: 'string',
      primary: true,
    },
  },
  query: {
    alwaysPaginate: true,
    sort: [{ field: 'createdAt', order: 'DESC' }],
    softDelete: false,
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
// @CrudAuth({
//   persist: (req) => ({
//     createdBy: req.user.id,
//   }),
//   filter: (req) =>
//     req.user.role == ROLE.ADMIN ? { createdBy: req.user.id } : null,
// })
@ApiTags('admin/users')
@Controller('admin/users')
export class AdminUserController implements CrudController<UserEntity> {
  constructor(public service: UserService) {}

  get base(): CrudController<UserEntity> {
    return this;
  }

  @Patch(':id/change-password')
  @ApiBody({ type: () => AdminChangePassword })
  async changePassword(
    @Param('id') id: string,
    @Body() payload: AdminChangePassword,
  ) {
    return this.service.changePassword(id, payload);
  }

  @Override()
  createOne(
    @ParsedRequest() req: CrudRequest,
    @ParsedBody() dto: UserEntity,
    @AuthUser() user,
  ) {
    // if (user.role == ROLE.ADMIN) {
    //   throw new ForbiddenException(
    //     'Bạn không đủ quyền để tạo tài khoản cấp cao',
    //   );
    // }

    return this.base.createOneBase(req, dto);
  }

  @Delete('bulk')
  async deleteBulk(@Body() payload: IdsDto) {
    return this.service.deleteBulk(payload.ids);
  }
}
