import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { DailyChannelTrace } from '../../entities';
import { DailyChannelTraceService } from './daily-channel-trace.service';

@Auth()
@Crud({
  model: {
    type: DailyChannelTrace,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    /*    join: {
      colorCombinations: { eager: false },
    }, */
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('client/daily-channel-traces')
@Controller('client/daily-channel-traces')
export class ClientDailyChannelTraceController
  implements CrudController<DailyChannelTrace>
{
  constructor(public service: DailyChannelTraceService) {}
}
