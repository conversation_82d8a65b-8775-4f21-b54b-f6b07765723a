import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { LatestWarningDailyVideoView } from '../../entities/view';
import { Auth } from '../../decorators';
import { LatestWarningDailyVideoService } from './latest-warning-video.service';

@Auth()
@Crud({
  model: {
    type: LatestWarningDailyVideoView,
  },
  params: {
    id: {
      field: 'id',
      type: 'string',
      primary: true,
    },
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    join: {
      author: {
        eager: false,
        required: true,
        allow: [
          'id',
          'profileId',
          'ytbChannelId',
          'profileEmail',
          'uniqueId',
          'nickname',
        ],
      },
    },
  },
  routes: {
    only: ['getManyBase'],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/latest-warning')
@Controller('admin/latest-warning')
export class AdminLatestWarningDailyVideoController
  implements CrudController<LatestWarningDailyVideoView>
{
  constructor(public service: LatestWarningDailyVideoService) {}
}
