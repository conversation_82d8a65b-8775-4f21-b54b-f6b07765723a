import { UseGuards, applyDecorators, SetMetadata } from '@nestjs/common';
import { ApiSecurity, ApiUnauthorizedResponse } from '@nestjs/swagger';
import { ApiKeyGuard } from '../guards/api-key.guard';

export function AuthApiKey(key?: string) {
  return applyDecorators(
    SetMetadata('__AuthApiKey__', key),
    UseGuards(ApiKeyGuard),
    ApiSecurity('x-api-key'),
    ApiUnauthorizedResponse({ description: 'Unauthorized' }),
  );
}
