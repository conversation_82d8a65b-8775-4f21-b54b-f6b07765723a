import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { IdsDto } from '../../common/dto';
import { decodeSearchAfter } from '../../common/utils/base64';
import { Auth } from '../../decorators';
import { Video } from '../../entities';
import { VideoService } from './video.service';

@Auth()
@Crud({
  params: {
    id: {
      field: 'id',
      type: 'string',
      primary: true,
    },
  },
  model: {
    type: Video,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    join: {
      author: { eager: false },
    },
  },
  routes: {
    only: ['getOneBase', 'getManyBase'],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('client/videos')
@Controller('client/videos')
export class ClientVideoController implements CrudController<Video> {
  constructor(public service: VideoService) {}
}
