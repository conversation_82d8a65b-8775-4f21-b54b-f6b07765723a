import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { sleep } from '../../../common/utils';
import { SourceKind } from '../../../entities';
import { ATGenAuthorCollectorService } from '../at-gen-video/at-gen-video.service.collector';
import { AuthorCollectorService } from '../author.service.collector';

const CONCURRENCY = parseInt(process.env.WORKER_CONCURRENCY) || 10;

@Processor('author', { concurrency: CONCURRENCY })
export class AuthorProcessor extends WorkerHost {
  private logger = new Logger(AuthorProcessor.name);

  constructor(
    public authorCollectorService: AuthorCollectorService,
    public atGenAuthorCollectorService: ATGenAuthorCollectorService,
  ) {
    super();
  }

  async process(
    job: Job<{ uniqueId: string; sourceKind: SourceKind }>,
    token?: string,
  ): Promise<any> {
    const authorId = job.data.uniqueId;
    this.logger.debug(`Start: ${authorId}, kind: ${job.data.sourceKind}`);

    if (job.data.sourceKind == SourceKind.ATGenVideo) {
      await this.atGenAuthorCollectorService.fetchAllVideoByChannelId(authorId);
    } else {
      if (
        !(await this.authorCollectorService.isAuthorQualified(authorId, 100))
      ) {
        await job.log('Author not qualified');
        await sleep(1_000);
        return;
      }

      for await (const [
        err,
        count,
      ] of this.authorCollectorService.handleCollectorIterator(authorId)) {
        if (err) {
          await job.log('Author existed');
          await sleep(1_000);
          return;
        }
        await job.log(`count: ${count}`);
      }
    }

    this.logger.log(`Done ${authorId}`);
  }
}
