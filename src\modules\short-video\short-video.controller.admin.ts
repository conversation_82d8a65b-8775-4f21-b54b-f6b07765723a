import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Crud, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { ShortVideo } from '../../entities';
import { ShortVideoService } from './short-video.service';

@Auth()
@Crud({
  model: {
    type: ShortVideo,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/short-videos')
@Controller('admin/short-videos')
export class AdminShortVideoController implements CrudController<ShortVideo> {
  constructor(public service: ShortVideoService) {}

  @Get('test')
  test() {
    return this.service.upsertShortVideo([
      {
        videoId: 's4WRfKBesWA',
        description: 'Trung bình chúng tôi quay video kiểu: #shorts',
        thumbnail:
          'https://i.ytimg.com/vi/s4WRfKBesWA/maxres2.jpg?sqp=-oaymwEoCIAKENAF8quKqQMcGADwAQH4AbYIgAKAD4oCDAgAEAEYZSBSKEQwDw==&rs=AOn4CLAxMIOeA-WuoVr1sSXglSyC_TQchQ',
        viewCount: '15397',
        likeCount: '731',
        title: 'Trung bình chúng tôi quay video kiểu: #shorts',
        ytbChannelId: 'UCY5D58OzoREAEDB8qdIJY6Q',
        duration: '36',
        published: 'Aug 10, 2023',
      },
    ]);
  }
}
