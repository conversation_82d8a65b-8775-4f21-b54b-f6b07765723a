import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsEnum, IsString } from 'class-validator';

export enum AllowedActionStatus {
  Active = 'active',
  Copyright = 'copyright',
  ProfileDied = 'profile_died',
  TiktokDied = 'tiktok_died',
  Inactive = 'inactive',
}

export class ClientBulkStatusAction {
  @ApiProperty({ enum: AllowedActionStatus })
  @IsString()
  @IsEnum(AllowedActionStatus, {
    message: `status must be either 'copyright' or 'active' or 'profile_died' or 'tiktok_died' or 'inactive'`,
  })
  status: AllowedActionStatus;

  @ApiProperty()
  @IsString({ each: true })
  @ArrayMinSize(1)
  profileIds: string[];
}
