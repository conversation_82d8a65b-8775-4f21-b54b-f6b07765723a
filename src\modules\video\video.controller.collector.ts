import { InjectQueue } from '@nestjs/bullmq';
import { Body, Controller, Post, ValidationPipe } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Queue } from 'bullmq';
import { Auth<PERSON><PERSON><PERSON><PERSON> } from '../../decorators';
import { CreateManyVideosDto } from './request/video-collector.dto';
import { VideoService } from './video.service';

@AuthApiKey('API_KEY_COLLECTOR')
@ApiTags('collector')
@Controller('collector/videos')
export class CollectorVideoController {
  constructor(
    // @InjectQueue('video') private videoQueue: Queue,
    public service: VideoService,
  ) {}

  @Post('push')
  async handleCollector(
    @Body(
      new ValidationPipe({
        transform: true,
        skipMissingProperties: false,
        whitelist: true,
      }),
    )
    payload: CreateManyVideosDto,
  ) {
    return this.service.handleCollector(payload.bulk);
    // const jobName = 'handleCollector';
    // await this.service.handleCollector(payload.bulk);
    // await this.videoQueue.addBulk(
    //   payload.bulk.map((info) => ({
    //     name: jobName,
    //     data: info,
    //     opts: { jobId: `${info.service}-${info.sourceId}` },
    //   })),
    // );
    // return { message: 'job enqueue', payload };
  }
}
