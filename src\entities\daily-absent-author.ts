import { IsString } from 'class-validator';
import { Column, Entity, ManyToOne, Unique } from 'typeorm';
import { WithIdAndTimestamp } from './withIdAndTimestamp';
import { Author } from './author';

@Entity('daily_absent_author')
@Unique(['authorId', 'date'])
export class DailyAbsentAuthor extends WithIdAndTimestamp {
  @ManyToOne(() => Author)
  author: Author;

  @Column({ type: 'bigint' })
  authorId: string;

  @Column({ type: 'bigint', nullable: true })
  accountId: string | null;

  @Column({ type: 'bigint', nullable: true })
  profileId: string;

  @Column()
  status: string;

  @IsString()
  @Column({
    type: 'date',
  })
  date: Date;
}
