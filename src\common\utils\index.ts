import * as crypto from 'crypto';

export function generateCacheKey(query) {
  const queryStr = JSON.stringify(query);
  return crypto.createHash('sha256').update(queryStr).digest('hex');
}

export const CACHE_PREFIX = 'app:query:';

export function generateShortCacheKey(query) {
  const queryStr = JSON.stringify(query);
  const hash = crypto.createHash('md5').update(queryStr).digest('hex');
  return `${CACHE_PREFIX}${hash}`;
}

export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function camelToSnakeCase(str) {
  return str.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();
}

export function isIdleTimeInvalid(idle?: number): boolean {
  return !idle || idle < 720;
}

/**
 *
 * @param min
 * @param max
 * @returns The maximum is inclusive and the minimum is inclusive
 */
export function getRandomIntInclusive(min, max) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1) + min);
}
