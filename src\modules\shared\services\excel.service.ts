import { Injectable } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class ExcelService {
  createWorkbookStream(fileName: string): {
    workbook: ExcelJS.stream.xlsx.WorkbookWriter;
    filePath: string;
  } {
    const filePath = path.join(__dirname, '../../tmp', fileName);
    if (!fs.existsSync(path.dirname(filePath))) {
      fs.mkdirSync(path.dirname(filePath), { recursive: true });
    }

    const writableStream = fs.createWriteStream(filePath);
    const workbook = new ExcelJS.stream.xlsx.WorkbookWriter({
      stream: writableStream,
    });
    return { workbook, filePath };
  }
}
