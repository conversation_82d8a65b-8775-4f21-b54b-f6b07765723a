import { getRedisClient } from './use-adapter';
import { Logger } from '@nestjs/common';

export function RedisLock(lockKey: string, ttlSeconds: number = 600) {
  const logger = new Logger('RedisLockDecorator');

  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const functionName = `${target.constructor.name}.${propertyKey}`;
      logger.debug(`Attempting to acquire lock for function: ${functionName}`);

      const redisClient = getRedisClient();
      const lockAcquired = await redisClient.set(
        lockKey,
        'locked',
        'EX',
        ttlSeconds,
        'NX',
      );

      if (!lockAcquired) {
        logger.warn(
          `Lock for key "${lockKey}" is already acquired. Function: ${functionName}`,
        );
        return;
      }

      try {
        logger.debug(`Lock acquired for function: ${functionName}`);
        return await originalMethod.apply(this, args);
      } finally {
        await redisClient.del(lockKey);
        logger.debug(`Lock released for function: ${functionName}`);
      }
    };

    return descriptor;
  };
}
