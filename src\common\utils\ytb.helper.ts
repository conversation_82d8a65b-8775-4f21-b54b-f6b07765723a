/**
 * Remove null byte and any control characters (ASCII 0-31 except 9, 10, 13)
 */
export function sanitizeString(input: string): string {
  return input?.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') || '';
}

export function parseViewCount(viewCountStr: string): number {
  const multipliers = {
    K: 1_000, // Thousand
    M: 1_000_000, // Million
    B: 1_000_000_000, // Billion
  };
  const match = viewCountStr?.match(/([\d,.]+)([KMB]?)/);
  if (!match) {
    return null;
  }
  const number = parseFloat(match[1].replace(/,/g, ''));
  const suffix = match[2];
  return Math.round(number * (multipliers[suffix] || 1));
}
