import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, instanceToPlain } from 'class-transformer';
import { IsBoolean, IsOptional, IsString, MaxLength } from 'class-validator';
import { Column, Entity } from 'typeorm';
import { RoleType } from '../common/constants/enum-entity';
import { WithIdAndTimestamp } from './withIdAndTimestamp';

@Entity('users')
export class UserEntity extends WithIdAndTimestamp {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Column({ select: false, nullable: true })
  @Exclude({ toPlainOnly: true })
  password: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, unique: true, length: 32 })
  @MaxLength(32)
  username: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  firstName: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  lastName: string;

  @ApiProperty()
  @IsString()
  @MaxLength(14)
  @Column({ nullable: true, length: 14 })
  phoneNumber: string;

  @ApiProperty()
  @IsString()
  @Column({ unique: true })
  email: string;

  @ApiProperty()
  @IsBoolean()
  @Column({ default: true })
  isActive: boolean;

  @ApiPropertyOptional()
  @IsString()
  @Column({
    default: RoleType.USER,
  })
  role: string;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true, type: 'jsonb' })
  rolePermission: unknown | null;

  toJSON() {
    return instanceToPlain(this);
  }
}
