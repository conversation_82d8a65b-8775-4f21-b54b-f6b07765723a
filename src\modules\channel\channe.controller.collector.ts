import { ApiTags } from '@nestjs/swagger';
import { Auth<PERSON><PERSON><PERSON><PERSON> } from '../../decorators';
import { Body, Controller, Param, Post } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { UniqueIdsDto } from '../../common/dto/unique-ids.dto';

@AuthApiKey('API_KEY_COLLECTOR')
@ApiTags('collector')
@Controller('collector/channels')
export class CollectorAuthorController {
  constructor(@InjectQueue('channel') private channelQueue: Queue) {}

  @Post()
  async handlerCollectorBulk(@Body() payload: UniqueIdsDto) {
    const jobName = 'fetch-channel';
    await this.channelQueue.addBulk(
      payload.uniqueIds.map((channelId) => ({
        name: jobName,
        data: { channelId },
        opts: { jobId: `channel-${channelId}` },
      })),
    );
    return { message: 'job enqueue' };
  }
}
