import {
  Body,
  Controller,
  Get,
  Headers,
  HttpCode,
  HttpStatus,
  Patch,
  Post,
} from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Auth, AuthApi<PERSON>ey, AuthUser } from '../../../decorators';
import { AuthRefreshToken } from '../../../decorators';
import { AuthService } from '../service/auth.service';
import { LoginRequestDto } from '../dto/login-request.dto';
import { LoginResponseDto } from '../dto/login-response.dto';
import { RegisterDto } from '../dto/register.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AuditEvent } from '../../../event';
import { RealIP } from '../../../decorators/real-ip.decorator';
import { UpdateMeDto } from '../dto/me.dto';
import { ChangePasswordDto } from '../dto/change-password';

@Controller('auth')
@ApiTags('auth')
export class AuthController {
  constructor(
    public readonly authService: AuthService,
    private eventEmitter: EventEmitter2,
  ) {}

  @AuthApiKey()
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Register user with email and password return the access token',
  })
  @ApiOkResponse({
    type: LoginResponseDto,
    description: 'User info with access token',
  })
  async userRegister(
    @Body() userRegisterDto: RegisterDto,
  ): Promise<LoginResponseDto> {
    return this.authService.userRegister(userRegisterDto);
  }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Login user with email and password return the access token',
  })
  @ApiOkResponse({
    type: LoginResponseDto,
    description: 'User info with access token',
  })
  async userLogin(
    @Headers() headers,
    @RealIP() ip: string,
    @Body() userLogin: LoginRequestDto,
  ) {
    const res = await this.authService.validateUser(userLogin);
    this.eventEmitter.emit(AuditEvent.LOGIN, {
      ip,
      headers,
      userId: res.data.id,
    });

    return res;
  }

  @Post('refresh-token')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Generate access token From refresh token',
  })
  @ApiOkResponse({
    type: LoginResponseDto,
    description: 'User info with access token',
  })
  @AuthRefreshToken()
  async refreshToken(
    @Headers() headers,
    @RealIP() ip: string,
    @AuthUser() user,
  ) {
    const res = await this.authService.generateToken(user);
    this.eventEmitter.emit(AuditEvent.REFRESH_TOKEN, {
      ip,
      headers,
      userId: res.data.id,
    });
    return res;
  }

  @Get('me')
  @Auth()
  async getMe(@AuthUser() user) {
    return this.authService.getUserInfo(user.id);
  }

  @Patch('me')
  @Auth()
  async updateMe(@AuthUser() user, @Body() payload: UpdateMeDto) {
    return this.authService.updateMe(user.id, payload);
  }

  @Patch('change-password')
  @Auth()
  async changePassword(@AuthUser() user, @Body() payload: ChangePasswordDto) {
    return this.authService.changePassword(user.id, payload);
  }
}
