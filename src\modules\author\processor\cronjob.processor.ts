import { InjectQueue, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job, Queue } from 'bullmq';
import { noop } from 'rxjs';
import { In, IsNull } from 'typeorm';
import { Status } from '../../../common/enum/status.enum';
import { SourceKind } from '../../../entities';
import { uploadLargeCsvToDrive } from '../../../integrations/google-drive';
import { ChannelService } from '../../channel/channel.service';
import { DailyAbsentAuthorService } from '../../daily-absent-author/daily-absent-author.service';
import { DailyChannelTraceService } from '../../daily-channel-trace/daily-channel-trace.service';
import { sendMessage } from '../../integrate/lark';
import { RoundLapTrackingService } from '../../round-lap-tracking/round-lap-tracking.service';
import { ATGenAuthorCollectorService } from '../at-gen-video/at-gen-video.service.collector';
import { AuthorService } from '../author.service';
import { AuthorCollectorService } from '../author.service.collector';
import { MultiLoginAlertService } from '../../../integrations/multilogin/multi-alert.service';

const CONCURRENCY = parseInt(process.env.WORKER_CONCURRENCY) || 10;

@Processor('cronjob', { concurrency: CONCURRENCY })
export class CronjobProcessor extends WorkerHost {
  private logger = new Logger(CronjobProcessor.name);

  constructor(
    public authorCollectorService: AuthorCollectorService,
    private dailyChannelTraceService: DailyChannelTraceService,
    private channelService: ChannelService,
    private authorService: AuthorService,
    private roundLapTrackingService: RoundLapTrackingService,
    public atGenAuthorCollectorService: ATGenAuthorCollectorService,
    public dailyAbsentAuthorService: DailyAbsentAuthorService,
    @InjectQueue('new-video') private newVideoQueue: Queue,
    @InjectQueue('dlq') private dlqQueue: Queue,
    @InjectQueue('channel') private channelQueue: Queue,
    @InjectQueue('author') private authorQueue: Queue,
    private multiLoginAlertService: MultiLoginAlertService,
  ) {
    super();
  }

  async process(job: Job<any>, token?: string): Promise<any> {
    this.logger.debug(`JobName: ${job.name}`);
    if ('fetch-new-author-at-gen-video' == job.name) {
      await this.fetchNewAuthorATGenVideo();
      return;
    }

    if ('track-daily-round' == job.name) {
      const currentLap =
        await this.roundLapTrackingService.getTodayTrackingLap();
      const activeLapSize =
        await this.roundLapTrackingService.getActiveLapSize();
      sendMessage(
        `[${currentLap.date}] Lap ${currentLap.totalLap} | active_lap: ${activeLapSize}`,
      )
        .then(noop)
        .catch(console.error);

      const currentLapFlowParentChild =
        await this.roundLapTrackingService.getTodayTrackingLapFlowParentChild();
      const statsFlowParentChild =
        await this.roundLapTrackingService.getStatsFlowParentChild();

      sendMessage(
        `[Flow Mail child-parent] 
          - Date: ${currentLapFlowParentChild.date}
          - Lap ${currentLapFlowParentChild.totalLap} 
          - Total Parent: ${statsFlowParentChild.totalParents}
          - Total Child: ${statsFlowParentChild.totalChild}`,
      )
        .then(noop)
        .catch(console.error);

      return;
    }

    if ('fetch-new-video' == job.name) {
      await job.log('Fetch new video');
      await this.fetchNewVideo();

      await job.log('Update Detail History');
      await this.dailyChannelTraceService.updateDetailHistory();
      return;
    }

    if ('fetch-language' == job.name) {
      await this.fetchAuthorLanguage();
      return;
    }

    if ('fetch-channel-stats' == job.name) {
      await this.fetchChannelStats();
      return;
    }

    if ('daily-absent-author' == job.name) {
      await this.processDailyAbsentAuthor();
      return;
    }
  }

  async fetchNewVideo() {
    const jobName = 'handleCollector';
    const batchSize = 1000; // Adjust batch size as needed
    let offset = 0;

    while (true) {
      // Fetch a batch of authors
      const authors = await this.authorCollectorService.repo.find({
        select: ['uniqueId', 'lastFetchedVideoId', 'sourceKind'],
        // where: {
        //   status: Status.Active,
        // },
        order: { createdAt: 'ASC' },
        skip: offset,
        take: batchSize,
      });

      if (!authors.length) {
        break; // Exit loop if no more authors
      }

      // Enqueue jobs for the current batch
      await this.newVideoQueue.addBulk(
        authors.map((author) => ({
          name: jobName,
          data: {
            uniqueId: author.uniqueId,
            lastFetchedVideoId: author.lastFetchedVideoId,
            sourceKind: author.sourceKind,
          },
          opts: { jobId: `author-${author.uniqueId}` },
        })),
      );

      offset += batchSize;
    }
  }

  async fetchAuthorLanguage() {
    const batchSize = 10;
    let offset = 0;

    while (true) {
      // Fetch a batch of authors
      const authors = await this.authorCollectorService.repo.find({
        select: ['uniqueId', 'id'],
        order: { createdAt: 'ASC' },
        where: {
          language: IsNull(),
        },
        skip: offset,
        take: batchSize,
      });

      if (!authors.length) {
        break;
      }

      const results = await Promise.allSettled(
        authors.map(async (author) => {
          const isEnglishSupported =
            await this.authorCollectorService.isAuthorQualified(
              author.uniqueId,
              50,
            );
          return isEnglishSupported ? author.id : null;
        }),
      );

      // Extract only successful results
      const englishAuthorIds = results
        .filter(
          (result): result is PromiseFulfilledResult<string | null> =>
            result.status === 'fulfilled' && result.value !== null,
        )
        .map((result) => result.value);

      if (englishAuthorIds.length) {
        await this.authorCollectorService.repo.update(
          { id: In(englishAuthorIds) },
          { language: 'en' },
        );
      }

      offset += batchSize;
    }
  }

  async fetchChannelStats() {
    // const jobName = 'fetch-channel';
    // const batchSize = 1000;
    // let offset = 0;

    // Step 1: syncChannelMultipleLogin
    await this.authorService.syncChannelMultipleLogin().catch(noop);

    return;
    // await sleep(1_000);

    // // Step 2: fetch new chanel id from author
    // const newChannels = await this.authorCollectorService.repo
    //   .createQueryBuilder('a')
    //   .leftJoin(Channel, 'c', 'a.ytbChannelId = c.ytbChannelId')
    //   .andWhere('a.ytbChannelId IS NOT NULL')
    //   .andWhere('c.ytbChannelId IS NULL')
    //   .select('a.ytbChannelId')
    //   .getMany();

    // await this.channelQueue.addBulk(
    //   newChannels.map((channel) => ({
    //     name: jobName,
    //     data: {
    //       channelId: channel.ytbChannelId,
    //     },
    //     opts: { jobId: `channel-${channel.ytbChannelId}` },
    //   })),
    // );

    // // Step 3: fetch all channel
    // while (true) {
    //   // Fetch a batch of channels
    //   const channels = await this.channelService.repo.find({
    //     select: ['ytbChannelId', 'id'],
    //     order: { createdAt: 'ASC' },
    //     skip: offset,
    //     take: batchSize,
    //   });

    //   if (!channels.length) {
    //     break;
    //   }

    //   // Enqueue jobs for the current batch
    //   await this.channelQueue.addBulk(
    //     channels.map((channel) => ({
    //       name: jobName,
    //       data: {
    //         channelId: channel.ytbChannelId,
    //       },
    //       opts: { jobId: `channel-${channel.ytbChannelId}` },
    //     })),
    //   );

    //   offset += batchSize;
    // }
  }

  async fetchNewAuthorATGenVideo() {
    const uniqueIds = await this.atGenAuthorCollectorService.fetchNewAuthor();

    if (!uniqueIds.length) {
      return;
    }

    await this.authorQueue.addBulk(
      uniqueIds.map((uniqueId) => ({
        name: `internal_fetch_new_at_author`,
        data: { uniqueId, sourceKind: SourceKind.ATGenVideo },
        opts: { jobId: `author-${uniqueId}` },
      })),
    );
  }

  async processDailyAbsentAuthor() {
    this.logger.debug('Start processing daily absent author');

    const yesterdayVN = new Date(); // server UTC, VN is UTC+7

    const startOfDayVNFormatted = yesterdayVN.toISOString().split('T')[0];

    const absentAuthor = await this.authorService.getAbsentAuthorsByDate(
      startOfDayVNFormatted,
    );

    const count = await this.dailyAbsentAuthorService.addBulkAbsentAuthors(
      absentAuthor,
      startOfDayVNFormatted,
    );

    this.logger.debug(
      `Added ${count} absent authors for date: ${startOfDayVNFormatted}`,
    );

    const activeAuthorsAbsentLastNDays =
      await this.dailyAbsentAuthorService.getAuthorsWithStatusForLastNDays(
        Status.Inactive,
        yesterdayVN,
        2,
      );

    if (!activeAuthorsAbsentLastNDays.length) {
      this.logger.debug('No active authors absent for 3 days');
      return;
    }

    const activeAuthorIdsSet = new Set(
      activeAuthorsAbsentLastNDays.map((author) => author.authorId),
    );

    const filteredAbsentAuthors = absentAuthor.filter((author) =>
      activeAuthorIdsSet.has(author.id),
    );

    this.logger.debug(
      `Found ${filteredAbsentAuthors.length} active authors absent for 3 days`,
    );

    sendMessage(
      `🚨🚨🚨
      Date: ${startOfDayVNFormatted}
      Found ${filteredAbsentAuthors.length} active authors absent for 3 days, csv uploading...`,
    );

    const csvFileName = `active_authors_absent_${startOfDayVNFormatted}.csv`;

    const googleDriveFolderId = process.env.GOOGLE_DRIVE_FOLDER_ID;
    const googleDriveClientId = process.env.GOOGLE_DRIVE_CLIENT_ID;
    const googleDriveClientSecret = process.env.GOOGLE_DRIVE_CLIENT_SECRET;
    const googleDriveRefreshToken = process.env.GOOGLE_DRIVE_REFRESH_TOKEN;
    const makeFilePublic = true;

    const uploadResult = await uploadLargeCsvToDrive(
      filteredAbsentAuthors,
      csvFileName,
      googleDriveFolderId,
      googleDriveClientId,
      googleDriveClientSecret,
      googleDriveRefreshToken,
      makeFilePublic,
      [
        { key: 'profileId', header: 'Profile Id' },
        { key: 'id', header: 'Author Id' },
        { key: 'accountId', header: 'Account Id' },
        { key: 'uniqueId', header: 'Unique ID' },
      ],
    );

    if (!uploadResult) {
      this.logger.error('Failed to upload CSV file to Google Drive');
      return;
    }

    sendMessage(`
      Date: ${startOfDayVNFormatted}
      Found ${filteredAbsentAuthors.length} active authors absent for 3 days
      CSV file: ${uploadResult.webViewLink}
      Download link: ${uploadResult.webContentLink}
      `)
      .then(noop)
      .catch(console.error);

    this.multiLoginAlertService
      .sendMessage([
        `Date: ${startOfDayVNFormatted}`,
        `Found ${filteredAbsentAuthors.length} active authors absent for 3 days`,
        `CSV file: ${uploadResult.webViewLink}`,
        `Download link: ${uploadResult.webContentLink}`,
      ])
      .then(noop)
      .catch(console.error);
  }
}
