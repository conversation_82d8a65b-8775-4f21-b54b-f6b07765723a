import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { convertKeysToCamelCase } from '../../common/utils/string.util';
import { SnapTikResponse } from './snaptik.dto';
import { getRandomIntInclusive, sleep } from '../../common/utils';
import { plainToInstance } from 'class-transformer';
import { detectLanguage } from '../../common/utils/language';

const userAgents = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
  'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
];

function _hasMore(cursor: string | null): boolean {
  return !!cursor && cursor !== '0' && !cursor.startsWith('-');
}

@Injectable()
export class SnapTikIntegrationService {
  private logger = new Logger(SnapTikIntegrationService.name);

  private readonly baseUrl = 'https://pro.snaptik.app/api';

  async fetchVideos(
    userInput: string,
    cursor?: string,
  ): Promise<SnapTikResponse> {
    const params = { userinput: userInput };
    if (cursor) params['cursor'] = cursor;
    const randomUA = userAgents[Math.floor(Math.random() * userAgents.length)];
    const maxRetries = 5;
    const baseDelay = 3_000;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await axios.get<SnapTikResponse>('/action', {
          params,
          baseURL: this.baseUrl,
          headers: {
            'User-Agent': randomUA,
          },
        });

        return plainToInstance(
          SnapTikResponse,
          convertKeysToCamelCase(response.data),
        );
      } catch (error) {
        console.error(
          `Attempt ${attempt} failed to fetch videos from SnapTik: ${error.message}`,
        );

        if (attempt === maxRetries) {
          // If it's the last attempt, throw the error
          throw new Error(
            `Failed to fetch videos from SnapTik API after ${maxRetries} attempts. ${JSON.stringify(
              {
                userInput,
                cursor,
                body: error.response?.data,
              },
            )}`,
          );
        }

        // Exponential backoff: Wait for 3s, 6s, 9s before the next retry
        const delay = baseDelay * attempt;
        await sleep(delay);
      }
    }
  }

  async fetchAllVideos(authorSourceId: string): Promise<SnapTikResponse[]> {
    let cursor: string | null = null;
    let hasMore = true;
    const results: SnapTikResponse[] = [];

    while (hasMore) {
      this.logger.debug(`Fetch Video ${authorSourceId} ${cursor}`);
      const response = await this.fetchVideos(authorSourceId, cursor);

      await sleep(getRandomIntInclusive(50, 200));

      // Store the current batch of results
      results.push(response);

      // Update cursor and hasMore
      cursor = response.cursor;
      hasMore = _hasMore(cursor);
    }

    return results;
  }

  async *fetchAllVideosIterator(
    authorSourceId: string,
    stopId = '',
  ): AsyncGenerator<SnapTikResponse> {
    let cursor: string | null = null;
    let hasMore = true;

    while (hasMore) {
      try {
        this.logger.debug(`Fetch Video ${authorSourceId} ${cursor}`);
        const response = await this.fetchVideos(authorSourceId, cursor);

        await sleep(getRandomIntInclusive(50, 100));

        // Check if the response contains the stopId
        if (
          stopId &&
          response.items &&
          response.items.some((video) => video.videoId === stopId)
        ) {
          this.logger.debug(`Stop ID ${stopId} found. Stopping fetch.`);
          yield response; // Optionally yield the final response containing the stopId
          break; // Exit the loop
        }

        // Yield the current response
        yield response;

        // Update cursor and hasMore
        cursor = response.cursor;
        hasMore = _hasMore(cursor);
      } catch (err) {
        this.logger.error(
          `Fetch failed for ${authorSourceId} at cursor ${cursor}:`,
          err,
        );
        return;
      }
    }
  }

  /**
   ** Language Requirement: The author primarily creates content in English (lang = 'en').
   ** Video Count Requirement: The author has published at least 100 videos.
   * @param authorSourceId
   * @returns
   */
  async isAuthorQualified(
    authorSourceId: string,
    amountVideo?: number,
  ): Promise<boolean> {
    this.logger.warn(
      `Check author ${authorSourceId} qualified with amount video ${amountVideo}`,
    );
    const LANGUAGE = 'en';
    const MINIMUM_VIDEO_COUNT = amountVideo || 100;

    let cursor: string | null = null;
    let hasMore = true;
    const titles: string[] = [];

    while (hasMore) {
      this.logger.debug(`Fetch Video ${authorSourceId} ${cursor}`);
      const response = await this.fetchVideos(authorSourceId, cursor);

      await sleep(getRandomIntInclusive(50, 200));

      // Store the current batch of results
      titles.push(...response.items.map((item) => item.title));

      if (titles.length >= MINIMUM_VIDEO_COUNT) {
        break;
      }

      // Update cursor and hasMore
      cursor = response.cursor;
      hasMore = _hasMore(cursor);
    }

    return (
      titles.length >= MINIMUM_VIDEO_COUNT &&
      detectLanguage(titles.filter((x) => !!x)) == LANGUAGE
    );
  }
}
