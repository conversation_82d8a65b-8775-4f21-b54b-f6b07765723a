NODE_ENV=development
SYNC_DB=1 # 1: sync, else: not sync
TZ=UTC #timezone

PORT=3210

DB_URL=postgres://postgres:123456@localhost:5432/base_project
DB_LOGGING=true

JWT_SECRET_KEY=duck
EXPIRE_TIME=36000s
RF_EXPIRE_TIME=360000s
API_KEY=API_KEY

# ======================= AWS =======================

AWS_ACCESS_KEY=
AWS_SECRET_KEY=
S3_BUCKET=
AWS_REGION=ap-southeast-1
S3_PREFIX=dev

# ======================= Redis =======================
REDIS_HOST=redis
REDIS_PORT=
REDIS_PASSWORD=

# ======================= Notification =======================
LARK_BOT_TOKEN=LARK_BOT_TOKEN
ALERT_CHANNEL=all

TELEGRAM_BOT_TOKEN=TELEGRA_TOKEN
TELEGRAM_CHAT_ID=TELEGRAM_GROUP_ID