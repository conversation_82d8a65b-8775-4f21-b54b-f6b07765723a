import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Video } from '../../entities';
import { DailyChannelTraceService } from '../daily-channel-trace/daily-channel-trace.service';
import { SettingService } from '../setting/setting.service';

@Injectable()
export class TaskStatsService {
  constructor(
    @InjectRepository(Video)
    private readonly videoRepository: Repository<Video>,
    private readonly settingService: SettingService,
    private readonly dailyChannelTraceService: DailyChannelTraceService,
  ) {}
}
