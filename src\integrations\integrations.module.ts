import { Module } from '@nestjs/common';
import { SnapTikIntegrationService } from './snaptik/snaptik.service';
import { MultiLoginIntegrationService } from './multilogin/multilogin.service';
import { ATGenVideoService } from './at-gen-video/at-gen-video.service';
import { MySnapTikIntegrationService } from './my-snaptik/my-snaptik.service';
import { MultiLoginAlertService } from './multilogin/multi-alert.service';

@Module({
  providers: [
    SnapTikIntegrationService,
    MySnapTikIntegrationService,
    MultiLoginIntegrationService,
    ATGenVideoService,
    MultiLoginAlertService,
  ],
  exports: [
    SnapTikIntegrationService,
    MySnapTikIntegrationService,
    MultiLoginIntegrationService,
    ATGenVideoService,
    MultiLoginAlertService,
  ],
  imports: [],
})
export class IntegrationsModule {}
