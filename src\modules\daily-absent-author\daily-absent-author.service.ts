import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { Repository } from 'typeorm';
import { DailyAbsentAuthor } from '../../entities';
import { generateSnowflakeId } from '../../common/utils/snowflake.util';
import { Status } from '../../common/enum/status.enum';
import { CrudRequest } from '@nestjsx/crud';

@Injectable()
export class DailyAbsentAuthorService extends TypeOrmCrudService<DailyAbsentAuthor> {
  constructor(
    @InjectRepository(DailyAbsentAuthor) repo: Repository<DailyAbsentAuthor>,
  ) {
    super(repo);
  }

  async addBulkAbsentAuthors(
    absentAuthors: {
      id: string;
      accountId: string;
      profileId: string;
      uniqueId: string;
      isAbsent: boolean;
    }[],
    date: string,
  ): Promise<number> {
    const chunkSize = 5000;

    const bulk = absentAuthors.map((author) => ({
      id: generateSnowflakeId(),
      authorId: author.id,
      accountId: author.accountId || null,
      profileId: author.profileId || null,
      status: author.isAbsent ? Status.Inactive : Status.Active,
      date: date,
    }));

    let count = 0;

    for (let i = 0; i < bulk.length; i += chunkSize) {
      const chunk = bulk.slice(i, i + chunkSize);
      const chunkResult = await this.repo
        .createQueryBuilder()
        .insert()
        .into(DailyAbsentAuthor)
        .values(chunk)
        .orUpdate(['status', 'account_id', 'profile_id'], ['author_id', 'date'])
        .returning('id')
        .execute();

      count += chunkResult.raw.length;
    }
    return count;
  }

  async getAuthorsWithStatusForLastNDays(
    status: string,
    fromDate: Date,
    nDays: number,
  ): Promise<{ authorId: string; absentCount: number }[]> {
    const date = new Date(fromDate);
    date.setDate(date.getDate() - nDays);
    const dateString = date.toISOString().split('T')[0];

    return this.repo
      .createQueryBuilder('dailyAbsentAuthor')
      .select('dailyAbsentAuthor.authorId', 'authorId')
      .addSelect('COUNT(dailyAbsentAuthor.id)::int', 'absentCount')
      .where('dailyAbsentAuthor.status = :status', { status })
      .andWhere('dailyAbsentAuthor.date >= :date', { date: dateString })
      .groupBy('dailyAbsentAuthor.authorId')
      .having('COUNT(dailyAbsentAuthor.id) >= :nDays', { nDays: nDays + 1 })
      .getRawMany();
  }

  async getStatsFlows(req: CrudRequest) {
    const { options, parsed } = req;
    let builder = await this.createBuilder(parsed, options);

    builder = builder.leftJoin(
      'Author',
      'author',
      'author.id = "DailyAbsentAuthor".author_id',
    );
    builder.groupBy(`"DailyAbsentAuthor"."date"`);

    builder.select([
      `"DailyAbsentAuthor"."date"::text AS "date"`,
      `count(*)::int AS "total"`,
      // `COUNT(*) FILTER ( WHERE "DailyAbsentAuthor".status = 'inactive')::int AS "totalInactive"`,
      // `COUNT(*) FILTER ( WHERE "DailyAbsentAuthor".status = 'inactive' and author.parent_id IS NOT NULL)::int AS "parentChildInactive"`,
      // `COUNT(*) FILTER ( WHERE "DailyAbsentAuthor".status = 'inactive' and author.parent_id IS NULL)::int AS "normalInactive"`,

      `COUNT(*) FILTER ( WHERE "DailyAbsentAuthor".status = 'active')::int AS "totalActive"`,
      `COUNT(*) FILTER ( WHERE "DailyAbsentAuthor".status = 'active' and author.parent_id IS NOT NULL)::int AS "parentChildActive"`,
      `COUNT(*) FILTER ( WHERE "DailyAbsentAuthor".status = 'active' and author.parent_id IS NULL)::int AS "normalActive"`,
    ]);
    const data = await builder.getRawMany();
    return { data };
  }
}
