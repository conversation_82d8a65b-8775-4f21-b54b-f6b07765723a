import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ITypeOrmCrudService } from '../../common/ITypeOrmCrudService';

import { Audit } from '../../entities';
import { OnEvent } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import { AUDIT_TYPE } from '../../common/enum/audit.enum';
import { AuditEvent } from '../../event';

@Injectable()
export class AuditService extends ITypeOrmCrudService<Audit> {
  constructor(@InjectRepository(Audit) repo) {
    super(repo);
  }

  @OnEvent(AuditEvent.LOGIN, { async: true })
  async handleLogin(info) {
    await this.repo.save(
      plainToInstance(Audit, { ...info, type: AUDIT_TYPE.LOGIN }),
    );
  }

  @OnEvent(AuditEvent.REFRESH_TOKEN, { async: true })
  async handleRefreshToken(info) {
    await this.repo.save(
      plainToInstance(Audit, { ...info, type: AUDIT_TYPE.REFRESHER_TOKEN }),
    );
  }
}
