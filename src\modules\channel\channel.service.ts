import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { Author, Channel } from '../../entities';
import { DeepPartial, getRepository, Repository } from 'typeorm';
import { generateSnowflakeId } from '../../common/utils/snowflake.util';
import { ChannelHistoryService } from '../channel-history/channel-history.service';
import { getVNDayRange } from '../../common/utils/date.util';

@Injectable()
export class ChannelService extends TypeOrmCrudService<Channel> {
  constructor(
    @InjectRepository(Channel) public readonly repo: Repository<Channel>,
    private channelHistoryService: ChannelHistoryService,
  ) {
    super(repo);
  }

  async upsertChannel(dto: DeepPartial<Channel>) {
    const existing = await this.repo.findOne({
      where: { ytbChannelId: dto.ytbChannelId },
      select: ['id'],
      withDeleted: true,
    });
    dto.id = existing?.id ?? generateSnowflakeId();

    await this.repo.upsert(dto, {
      conflictPaths: ['ytbChannelId'],
      skipUpdateIfNoValuesChanged: true,
    });

    const { startOfDayFormatted } = getVNDayRange();

    await this.channelHistoryService.upsertChannelHistory({
      id: generateSnowflakeId(),
      channelId: dto.id,
      viewCount: dto.viewCount,
      subscriberCount: dto.subscriberCount,
      videoCount: dto.videoCount,
      date: startOfDayFormatted,
    });

    await this.updateChannelDiffs(startOfDayFormatted, dto.id);
    await this.channelHistoryService.updateChannelDiffs(
      startOfDayFormatted,
      dto.id,
    );
    return { message: 'success' };
  }

  async updateChannelDiffs(givenDate: string, channelId: string) {
    return this.repo.query(
      `
      UPDATE channel c
      SET
          view_count_diff = COALESCE(c.view_count, 0) - COALESCE(ch.view_count, 0),
          subscriber_count_diff = COALESCE(c.subscriber_count, 0) - COALESCE(ch.subscriber_count, 0),
          video_count_diff = COALESCE(c.video_count, 0) - COALESCE(ch.video_count, 0)
      FROM channel_history ch
      WHERE c.id = $1
        AND ch.channel_id = c.id
        AND ch.date = (
            SELECT MAX(date) FROM channel_history
            WHERE channel_id = $1 AND date < $2
        );
      `,
      [channelId, givenDate],
    );
  }
}
