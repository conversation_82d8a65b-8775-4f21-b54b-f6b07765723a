import { Column, Entity, ManyToOne, Unique } from 'typeorm';
import { Author } from './author';
import { WithIdAndTimestamp } from './withIdAndTimestamp';
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

@Entity('daily_channel_trace')
@Unique(['authorId', 'date'])
export class DailyChannelTrace extends WithIdAndTimestamp {
  @ManyToOne(() => Author)
  author: Author;

  @Column()
  authorId: string;

  @ApiProperty()
  @IsString()
  @Column({
    type: 'date',
  })
  date: Date;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  @Column({ nullable: true, default: 0 })
  totalAllocations: number;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  @Column({ nullable: true, default: 0 })
  successfulCount: number;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  @Column({ nullable: true, default: 0 })
  failedCount: number;

  @ApiProperty()
  @IsString({ each: true })
  @IsOptional()
  @Column('text', { nullable: true, array: true })
  videoIds: string[];

  @ApiProperty()
  @IsOptional()
  @Column('timestamptz', { array: true, default: () => "'{}'" })
  reassignHistory: string[];

  @Column({ nullable: true })
  reservedAt: Date;
}
