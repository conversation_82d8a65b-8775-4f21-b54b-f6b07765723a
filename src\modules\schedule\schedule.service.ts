import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { sendMessage } from '../integrate';
import { getConnection } from 'typeorm';

@Injectable()
export class ScheduleService {
  private readonly logger = new Logger(ScheduleService.name);

  // @Cron('0 */5 * * * *')
  // async refreshVideoStatistics() {
  //   this.logger.debug(`RefreshVideoStatistics`);
  //   const connection = getConnection();

  //   await connection.query(
  //     `CREATE UNIQUE INDEX IF NOT EXISTS "UQ_m_view_video_statistics" ON video_statistics(video_id);`,
  //   );
  //   await connection.query(
  //     `REFRESH MATERIALIZED VIEW CONCURRENTLY video_statistics;`,
  //   );
  // }

  // fetchWithBaseURL(endpoint, options, baseUrl?: string) {
  //   const url = `${baseUrl || process.env.API_URL}${endpoint}`;

  //   options.headers = {
  //     'x-api-key': process.env.API_KEY,
  //     ...(options.headers || {}),
  //   };

  //   return fetch(url, options);
  // }

  // // * * * * * *
  // // | | | | | |
  // // | | | | | day of week
  // // | | | | months
  // // | | | day of month
  // // | | hours
  // // | minutes
  // // seconds (optional)

  // @Cron('0 1 17 * * *')
  // async handleCronJobExportExcel() {
  //   this.logger.debug(`Called when the current`);
  //   this._exportData().then(console.log).catch(console.error);
  // }

  // @Cron('0 1 5,17 * * *')
  // async handleCron() {
  //   this.logger.debug(`Called when the current`);
  //   this._promoteTaskSystem().then(console.log).catch(console.error);
  // }

  // private async _exportData() {
  //   try {
  //     const response = await this.fetchWithBaseURL(
  //       `/client/tasks/export-crawl-data`,
  //       {
  //         method: 'GET',
  //         headers: {
  //           accept: '*/*',
  //           'Content-Type': 'application/json',
  //         },
  //       },
  //       'http://scheduler:3210/api/v1',
  //     );

  //     if (!response.ok) {
  //       throw new Error(`HTTP error! Status: ${response.status}`);
  //     }

  //     const result = await response.json();

  //     return result;
  //   } catch (error) {
  //     console.error('Error posting keywords:', error);
  //     throw error;
  //   }
  // }

  // private async _promoteTaskSystem() {
  //   try {
  //     const response = await this.fetchWithBaseURL(
  //       `/client/tasks/system/promote`,
  //       {
  //         method: 'POST',
  //         headers: {
  //           accept: '*/*',
  //           'Content-Type': 'application/json',
  //         },
  //       },
  //     );

  //     if (!response.ok) {
  //       throw new Error(`HTTP error! Status: ${response.status}`);
  //     }

  //     const updateResult = await response.json();

  //     const message = '========= Promote Task System =========\n'.concat(
  //       updateResult.data.join('\n'),
  //     );
  //     sendMessage(message).then(console.log).catch(console.log);

  //     return updateResult;
  //   } catch (error) {
  //     console.error('Error posting keywords:', error);
  //     throw error;
  //   }
  // }
}
