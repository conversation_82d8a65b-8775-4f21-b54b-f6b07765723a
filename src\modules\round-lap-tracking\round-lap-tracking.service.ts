import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { Repository } from 'typeorm';
import { getVNDayRange } from '../../common/utils/date.util';
import { generateSnowflakeId } from '../../common/utils/snowflake.util';
import { RoundLapTracking, RoundLapTrackingFlowName } from '../../entities';
import { RedisCacheService } from '../cache/cache.service';

@Injectable()
export class RoundLapTrackingService extends TypeOrmCrudService<RoundLapTracking> {
  constructor(
    @InjectRepository(RoundLapTracking) repo: Repository<RoundLapTracking>,
    private readonly redisService: RedisCacheService,
  ) {
    super(repo);
  }

  async getTodayTrackingLap(): Promise<RoundLapTracking | null> {
    const { startOfDayFormatted } = getVNDayRange();
    let track = await this.repo.findOne({
      date: startOfDayFormatted,
      flowName: RoundLapTrackingFlowName.NORMAL,
    });
    if (!track) {
      const activeLapExists = await this.redisService
        .getRedisInstance()
        .exists('active_lap'); // 1 if key exists, 0 if it's gone

      track = await this.repo.save({
        date: startOfDayFormatted,
        id: generateSnowflakeId(),
        totalLap: activeLapExists,
        flowName: RoundLapTrackingFlowName.NORMAL,
      });
    }
    return track;
  }

  async incNewLap(): Promise<{ date: string; lap: number }> {
    const { startOfDayFormatted } = getVNDayRange();
    const track = await this.getTodayTrackingLap();
    await this.repo.increment(
      { date: startOfDayFormatted, flowName: RoundLapTrackingFlowName.NORMAL },
      'totalLap',
      1,
    );
    return { date: startOfDayFormatted, lap: track.totalLap + 1 };
  }

  async getActiveLapSize(): Promise<number> {
    const activeLapSize = await this.redisService
      .getRedisInstance()
      .llen('active_lap');
    return activeLapSize;
  }

  async getTodayTrackingLapFlowParentChild(): Promise<RoundLapTracking | null> {
    const { startOfDayFormatted } = getVNDayRange();
    let track = await this.repo.findOne({
      date: startOfDayFormatted,
      flowName: RoundLapTrackingFlowName.PARENT_CHILD,
    });
    if (!track) {
      const redisInstance = this.redisService.getRedisInstance();

      const totalIdleParent = await redisInstance.llen(
        // TaskChildService.ACTIVE_LIST_NAME,
        'tc:parents',
      ); // 1 if key exists, 0 if it's gone

      const totalParentRunning = await redisInstance.scard(
        // TaskChildService.LOCK_SET_NAME,
        'tc:lock:parents',
      );

      const countLap = totalIdleParent > 0 || totalParentRunning > 0 ? 1 : 0;

      track = await this.repo.save({
        date: startOfDayFormatted,
        id: generateSnowflakeId(),
        totalLap: countLap,
        flowName: RoundLapTrackingFlowName.PARENT_CHILD,
      });
    }
    return track;
  }

  async incNewLapFlowParentChild(): Promise<{ date: string; lap: number }> {
    const { startOfDayFormatted } = getVNDayRange();
    const track = await this.getTodayTrackingLapFlowParentChild();
    await this.repo.increment(
      {
        date: startOfDayFormatted,
        flowName: RoundLapTrackingFlowName.PARENT_CHILD,
      },
      'totalLap',
      1,
    );
    return { date: startOfDayFormatted, lap: track.totalLap + 1 };
  }

  async getStatsFlowParentChild(): Promise<{
    totalParents: number;
    totalChild: number;
  }> {
    const redisInstance = this.redisService.getRedisInstance();

    const parentKeys = await redisInstance.keys('tc:child_jobs:*');
    const totalChild = await Promise.all(
      parentKeys.map((key) => redisInstance.llen(key)),
    ).then((counts) => counts.reduce((sum, count) => sum + count, 0));

    return { totalParents: parentKeys.length, totalChild };
  }
}
