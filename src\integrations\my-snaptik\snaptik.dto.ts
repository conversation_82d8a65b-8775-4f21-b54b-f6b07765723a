import {
  IsString,
  IsN<PERSON>ber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { VideoCollectorDto } from '../../modules/video/request/video-collector.dto';

class AuthorDto {
  @IsString()
  @Expose()
  id: string;

  @IsString()
  @Expose()
  uniqueId: string;

  @IsString()
  @Expose()
  nickname: string;

  @IsString()
  @Expose()
  avatar: string;
}

export class VideoDto {
  @ValidateNested()
  @Expose()
  @Type(() => AuthorDto)
  @Expose()
  author: AuthorDto;

  @IsString()
  @Expose()
  videoId: string;

  @IsString()
  @Expose()
  awemeId: string;

  @IsString()
  @Expose()
  cover: string;

  @IsNumber()
  @Expose()
  videoDuration: number;

  @IsNumber()
  @Expose()
  videoSize: number;

  @IsOptional()
  @Expose()
  @IsString()
  @Expose()
  musicUrl: string;

  @IsString()
  @Expose()
  title: string;

  @IsOptional()
  @Expose()
  @IsString()
  @Expose()
  videoUrl: string;

  toCollectorVideo(): VideoCollectorDto {
    return {
      author: {
        sourceId: this.author.id,
        uniqueId: this.author.uniqueId,
        nickname: this.author.nickname,
      },
      sourceId: this.videoId,
      cover: this.cover,
      // videoDuration: this.videoDuration,
      // videoSize: this.videoSize,
      // playCount: this.stats.playCount,
      // shareCount: this.stats.shareCount,
      // downloadCount: this.stats.downloadCount,
      // musicUrl: this.musicUrl,
      // videoUrl: this.videoUrl,
      description: this.title,
    } as VideoCollectorDto;
  }
}

export class SnapTikResponse {
  @Expose() @Type(() => VideoDto) items: VideoDto[];
  @Expose() cursor: string;
  @Expose() hasMore: boolean;
  @Expose() msToken: string;
}
