import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';
import { Cron } from '@nestjs/schedule';
import { sendMessageViaToken } from '../integrate/lark';
import { CacheTtlSeconds, RedisCacheService } from '../cache/cache.service';
import { Author } from '../../entities/author';

const PREFIX = 'tc:';
const REST_NEW_LAP_PARENT_CHILD = 'tc:rest_new_lap';

@Injectable()
export class LapReportService extends TypeOrmCrudService<Author> {
  private readonly logger = new Logger(LapReportService.name);
  private readonly LARK_BOT_TOKEN_LAP_REPORT =
    process.env.LARK_BOT_TOKEN_LAP_REPORT;
  private static readonly REPORT_LOCK_KEY = 'lap_report_lock_key';
  private static readonly HASH_TIMELINE_NAME = `${PREFIX}timeline`;
  private static readonly THRESHOLD_MINUTES = 25 * 60; //25 hours

  constructor(
    @InjectRepository(Author) repo,
    private readonly redisService: RedisCacheService,
  ) {
    super(repo);
  }

  @Cron('0 0 */3 * * *') // Run every 3 hours
  async sendLapReportNotification() {
    const redisInstance = this.redisService.getRedisInstance();
    const lock = await redisInstance.set(
      LapReportService.REPORT_LOCK_KEY,
      'locked',
      'EX',
      CacheTtlSeconds.ONE_MINUTE,
      'NX',
    );
    if (!lock) {
      this.logger.warn('Report is running, wait...');
      return;
    }

    const today = new Date();
    try {
      const message = await this.getLapReport(today.toISOString().slice(0, 10));
      // Send to Lark
      await sendMessageViaToken(message, this.LARK_BOT_TOKEN_LAP_REPORT);
      this.logger.log('Notification sent to Lark successfully.');
    } catch (error) {
      this.logger.error('Failed to send notification to Lark', error);
    }
  }

  async getLapReport(date: string) {
    // Tracking lap flow parent-child
    const redisInstance = this.redisService.getRedisInstance();
    const restNewLap = await this.redisService.getValue(
      REST_NEW_LAP_PARENT_CHILD,
    );
    let message = `[Flow parent-child] Lap Tracking Report`;
    if (restNewLap) {
      this.logger.warn('Rest new lap, wait...');
      message = `${message}
        - Rest new lap, wait...
        `;
      return message;
    }
    const totalIdleParent = await redisInstance.llen('tc:parents');

    const totalParentRunning = await redisInstance.scard('tc:lock:parents');
    // Format message for Lark
    message = [
      message,
      ` Date: ${date}`,
      `   - Total Parent Idle (tc:parents): ${totalIdleParent}`,
      `   - Total Parent Running (tc:lock:parents): ${totalParentRunning}`,
    ].join('\n');
    if (Number(totalIdleParent) == 0) {
      const runningParents = await redisInstance.smembers('tc:lock:parents');
      for (const parentId of runningParents) {
        const childId = await redisInstance.lrange(
          `tc:child_jobs:${parentId}`,
          0,
          -1,
        );
        if (childId.length == 0) {
          message = [
            message,
            `   - Parent Id lock: ${parentId}. No child found`,
          ].join('\n');
        } else {
          message = [
            message,
            `   - Parent Id lock: ${parentId}: {${childId.join(', ')}} `,
          ].join('\n');
        }
      }
      if (totalParentRunning == 1) {
        const keys = await redisInstance.hkeys(
          LapReportService.HASH_TIMELINE_NAME,
        );
        if (keys.length > 0) {
          const lastParentId = keys[0];
          const timestamp = await redisInstance.hget(
            LapReportService.HASH_TIMELINE_NAME,
            lastParentId,
          );
          if (timestamp) {
            const currentTime = new Date().getTime();
            const diff = currentTime - parseInt(timestamp);
            if (diff > LapReportService.THRESHOLD_MINUTES * 60 * 1000) {
              const childId = await redisInstance.lrange(
                `tc:child_jobs:${lastParentId}`,
                0,
                -1,
              );
              message = [
                `⚠️⚠️⚠️ *Warning: Parent locked for more than ${(
                  LapReportService.THRESHOLD_MINUTES / 60
                ).toFixed(2)} hour!*`,
                ` Date: ${date}`,
                `   - ParentId: ${lastParentId}: {${
                  childId.join(', ') || 'empty'
                }} `,
                `   - Time lock (tc:timeline:${lastParentId}): ${new Date(
                  parseInt(timestamp),
                ).toLocaleString()}`,
                `   - Time now: ${new Date(currentTime).toLocaleString()}`,
                `   - Diff: ${(diff / (1000 * 60 * 60)).toFixed(2)} hour`,
              ].join('\n');
            }
          }
        }
      }
    }

    return message;
  }
}
