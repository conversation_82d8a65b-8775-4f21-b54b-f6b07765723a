import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  <PERSON>,
  Post,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { AuthApi<PERSON>ey } from '../../../decorators';

import { AssignVideosDto } from '../request/assign-videos.dto';
import { TaskServiceV3 } from './task.service';
import { TaskChildService } from './task-child.service';

@AuthApiKey()
@ApiTags('client/tasks')
@Controller({ path: 'tasks', version: '3' })
export class ClientTaskControllerV3 {
  private logger = new Logger(ClientTaskControllerV3.name);

  constructor(
    private readonly taskService: TaskServiceV3,
    private readonly taskChildService: TaskChildService,
  ) {}

  @Get('author')
  async getNextTaskTest() {
    return await this.taskService.getNextAuthor();
  }

  @Post('videos')
  async confirmAndGetVideos(@Body() payload: AssignVideosDto) {
    return this.taskService.confirmAndGetVideos(payload.authorId);
  }

  @Get('child')
  async getNextTaskChild() {
    return this.taskChildService.getNextChild();
  }

  @Patch('parent/:parentId')
  async confirmDoneParent(@Param('parentId') parentId: string) {
    return this.taskChildService.confirmDoneParent(parentId);
  }

  // @Get('refill-child')
  // async getRefill() {
  //   return this.taskChildService.refillNextLap();
  // }
}
