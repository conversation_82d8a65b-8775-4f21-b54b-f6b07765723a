import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { Column, Entity, ManyToOne, Unique } from 'typeorm';
import { WithIdAndTimestamp } from './withIdAndTimestamp';
import { ShortVideo } from './short-video';

@Entity('short_video_history')
@Unique(['shortVideoId', 'date'])
export class ShortVideoHistory extends WithIdAndTimestamp {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint' })
  viewCount: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint' })
  likeCount: string;

  @ApiProperty()
  @IsString()
  @Column({
    type: 'date',
  })
  date: Date;

  @ManyToOne(() => ShortVideo, (short) => short.histories)
  shortVideo: ShortVideo;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint', default: 0 })
  viewCountDiff: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint', default: 0 })
  likeCountDiff: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ type: 'bigint', nullable: true })
  shortVideoId: string;
}
