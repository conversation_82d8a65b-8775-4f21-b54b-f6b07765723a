import { B<PERSON><PERSON> } from 'buffer';

// Encode the `search_after` cursor to a string
export function encodeSearchAfter(cursor: any[]): string {
  return Buffer.from(JSON.stringify(cursor)).toString('base64');
}

// Decode the cursor when it is received as a string
export function decodeSearchAfter(cursor: string): any[] {
  return JSON.parse(Buffer.from(cursor, 'base64').toString('utf-8'));
}
