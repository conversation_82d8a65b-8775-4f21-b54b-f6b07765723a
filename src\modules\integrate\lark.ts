const LARK_BOT_TOKEN = process.env.LARK_BOT_TOKEN;

export async function sendMessage(message) {
  const url = `https://open.larksuite.com/open-apis/bot/v2/hook/${LARK_BOT_TOKEN}`;

  const body = {
    msg_type: 'text',
    content: {
      text: message,
    },
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
}

export async function sendMessageViaToken(message, token) {
  const url = `https://open.larksuite.com/open-apis/bot/v2/hook/${token}`;

  const body = {
    msg_type: 'text',
    content: {
      text: message,
    },
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
}
