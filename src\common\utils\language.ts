import { detect } from 'langdetect';

/**
 * Detect the most common language from a list of texts.
 * @param {string[]} texts - List of strings.
 * @returns {string} - Most common detected language code.
 */
export function detectLanguage(texts: string[]) {
  const langCount = new Map();

  for (const text of texts) {
    const detectedLanguages = detect(text.replace(/[\p{Emoji}]/gu, ''));
    if (detectedLanguages?.length > 0) {
      const topLang = detectedLanguages[0].lang; // Get the most confident language
      langCount.set(topLang, (langCount.get(topLang) || 0) + 1);
    }
  }

  // Find the most common language
  return [...langCount.entries()].reduce((a, b) => (b[1] > a[1] ? b : a))[0];
}
