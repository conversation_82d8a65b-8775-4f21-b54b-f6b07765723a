import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { Column, Entity, OneToMany } from 'typeorm';
import { WithIdAndTimestamp } from './withIdAndTimestamp';
import { ShortVideoHistory } from './short-video-history';

@Entity('short_video')
export class ShortVideo extends WithIdAndTimestamp {
  @ApiProperty()
  @IsString()
  @Column()
  ytbChannelId: string;

  @ApiProperty()
  @IsString()
  @Column({ unique: true })
  videoId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true })
  title: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true })
  description: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  thumbnail: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint' })
  viewCount: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint' })
  likeCount: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint' })
  duration: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true })
  published: Date;

  // @Column({ default: 0 })
  // totalProduct: number;

  @OneToMany(() => ShortVideoHistory, (history) => history.shortVideo)
  histories: ShortVideoHistory[];

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint', default: 0 })
  viewCountDiff: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true, type: 'bigint', default: 0 })
  likeCountDiff: string;
}
