import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { ShortVideoHistory } from '../../entities';
import { ShortVideoHistoryService } from './short-video-history.service';

@Auth()
@Crud({
  model: {
    type: ShortVideoHistory,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/short-video-histories')
@Controller('admin/short-video-histories')
export class AdminShortVideoHistoryController
  implements CrudController<ShortVideoHistory>
{
  constructor(public service: ShortVideoHistoryService) {}
}
