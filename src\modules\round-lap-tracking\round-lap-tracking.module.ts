import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RoundLapTracking } from '../../entities';
import { RoundLapTrackingService } from './round-lap-tracking.service';
import { AdminRoundLapTrackingController } from './round-lap-tracking.controller.admin';

@Module({
  imports: [TypeOrmModule.forFeature([RoundLapTracking])],
  providers: [RoundLapTrackingService],
  controllers: [AdminRoundLapTrackingController],
  exports: [RoundLapTrackingService],
})
export class RoundLapTrackingModule {}
