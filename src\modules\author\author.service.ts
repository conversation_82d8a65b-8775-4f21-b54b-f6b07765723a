import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { DeepPartial, In, IsNull, Not, Repository } from 'typeorm';
import { Status } from '../../common/enum/status.enum';
import { generateSnowflakeId } from '../../common/utils/snowflake.util';
import { Author, SourceKind, Video, VideoStatus } from '../../entities';
import { MultiLoginIntegrationService } from '../../integrations/multilogin/multilogin.service';
import { SnapTikIntegrationService } from '../../integrations/snaptik/snaptik.service';
import { CrudRequest } from '@nestjsx/crud';
import { noop } from 'rxjs';
import { AssignChannel } from './request/assign-author.dto';
import { ProfileAuthor } from '../../entities/profile-author';
import { ReassignChannel } from './request/reassign-author.dto';
import { sendMessage } from '../integrate/lark';
import { removePrefixAndCamelCase } from '../../common/utils/string.util';
import { RedisCacheService } from '../cache/cache.service';

@Injectable()
export class AuthorService extends TypeOrmCrudService<Author> {
  private logger = new Logger(AuthorService.name);

  constructor(
    @InjectRepository(Author) public readonly repo: Repository<Author>,
    @InjectRepository(Video) public readonly videoRepository: Repository<Video>,
    @InjectRepository(ProfileAuthor)
    public readonly repoProfileAuthor: Repository<ProfileAuthor>,
    private readonly snapTikService: SnapTikIntegrationService,
    private readonly multiLoginIntegrationService: MultiLoginIntegrationService,
    private readonly redisService: RedisCacheService,
    @InjectQueue('author-event') private eventQueue: Queue,
  ) {
    super(repo);
  }

  private async removeValueFromQueueRunning(
    authorId: string,
    parentId?: string,
  ) {
    this.logger.debug(
      `removeValueFromQueueRunning: authorId=${authorId}, parentId=${parentId}`,
    );

    const redisInstance = this.redisService.getRedisInstance();
    if (parentId) {
      const nameChildJobQueue = `tc:child_jobs:${parentId}`;
      await redisInstance.lrem(nameChildJobQueue, 0, authorId?.toString());
      return;
    }

    await redisInstance.lrem('active_lap', 0, authorId?.toString());
    await redisInstance.lrem('next_lap', 0, authorId?.toString());
  }

  async reassignTiktokDied(profileId: string) {
    const oldAuthor = await this.repo.findOne({
      where: { profileId },
    });
    if (!oldAuthor) {
      this.throwNotFoundException(
        `Author with profileId ${profileId} not found`,
      );
    }
    if (oldAuthor.status != Status.TiktokDied) {
      throw new ConflictException(
        `Author with profileId ${profileId} is not in ${Status.TiktokDied} status`,
      );
    }
    let newAuthor: Author;

    await this.repo.manager.transaction(async (transactionalEntityManager) => {
      // Update old author to inactive

      const queryBuilder = await transactionalEntityManager
        .createQueryBuilder(Author, 'author')
        .where('author.profileId IS NULL')
        .andWhere('author.status = :status', { status: Status.Active })
        .andWhere('author.sourceKind = :sourceKind', {
          sourceKind: SourceKind.Tiktok,
        })
        .orderBy('author.id', 'ASC')
        .limit(1);

      // Add `FOR UPDATE SKIP LOCKED` manually
      const sql = queryBuilder.getSql() + ' FOR UPDATE SKIP LOCKED';
      const parameters = [Status.Active, SourceKind.Tiktok];

      const rawResults = await transactionalEntityManager.query(
        sql,
        parameters,
      );

      newAuthor = removePrefixAndCamelCase(rawResults[0], 'author_') as Author;

      if (!newAuthor) {
        throw new NotFoundException(
          'There are currently no tiktok authors available for use',
        );
      }

      await transactionalEntityManager.update(Author, newAuthor.id, {
        profileEmail: oldAuthor.profileEmail,
        profileId: oldAuthor.profileId,
        accountId: oldAuthor.accountId,
        parentId: oldAuthor.parentId,
      });

      // soft delete old author
      await transactionalEntityManager.update(Author, oldAuthor.id, {
        profileId: oldAuthor.profileId + 'at',
        deletedAt: new Date(),
      });
    });

    return {
      message: 'success',
      author: {
        id: newAuthor.id,
        uniqueId: newAuthor.uniqueId,
        profileId: oldAuthor.profileId,
        accountId: oldAuthor.accountId,
        profileEmail: oldAuthor.profileEmail,
      },
    };
  }

  private _emitAlert(message: string, data: any) {
    sendMessage(
      `Author Service Alert: ${message}\nData: ${JSON.stringify(data)}`,
    )
      .then(noop)
      .catch(console.error);
  }

  async updateProfileChannel({
    uniqueId,
    profileId,
    accountId,
    email,
  }: ReassignChannel) {
    const author = await this.repo.findOne({ uniqueId });
    !author && this.throwNotFoundException(uniqueId);

    const queryBuilder = this.repo
      .createQueryBuilder('author')
      .select([
        'author.id',
        'author.profileId',
        'author.accountId',
        'author.uniqueId',
        'author.profileEmail',
      ])
      .where('author.profileId = :profileId', { profileId });

    if (accountId) {
      queryBuilder.orWhere('author.accountId = :accountId', { accountId });
    }
    if (email) {
      queryBuilder.orWhere('author.profileEmail = :email', { email });
    }

    const authorsWithProfileOrAccount = await queryBuilder.getMany();

    if (authorsWithProfileOrAccount.length > 1) {
      throw new ConflictException({
        message: `Conflict data`,
        current: authorsWithProfileOrAccount,
        req: {
          uniqueId,
          profileId,
          accountId,
          email,
        },
      });
    }

    const existingAuthor = authorsWithProfileOrAccount[0];

    if (existingAuthor && existingAuthor.uniqueId != uniqueId) {
      throw new ConflictException({
        message: `Conflict data`,
        current: authorsWithProfileOrAccount,
        req: {
          uniqueId,
          profileId,
          accountId,
          email,
        },
      });
    }

    const oldProfile = {
      profileId: author.profileId,
      accountId: author.accountId,
      email: author.profileEmail,
    };

    // Set null ytbChannelId, cronjob will sync new value
    await this.repo.update(author.id, {
      profileId,
      ...(accountId && { accountId }),
      ...(email && { profileEmail: email }),
      ytbChannelId: null,
    });
    const updatedAuthor = await this.repo.findOne({ id: author.id });

    return { oldProfile, author: updatedAuthor };
  }

  async updateOne(req: CrudRequest, dto: DeepPartial<Author>): Promise<Author> {
    const id = req.parsed.paramsFilter[0].value;

    if (id && dto.status) {
      const currAuthor = await this.repo.findOne(id);

      this._emitAlert('Change author status', {
        oldStatus: currAuthor.status,
        newStatus: dto.status,
        authorId: id,
        authorUniqueId: currAuthor.uniqueId,
        profileId: currAuthor.profileId,
        profileEmail: currAuthor.profileEmail,
        accountId: currAuthor.accountId,
      });

      // trigger flow verify it you
      if (dto.status == Status.Pause) {
        dto.pausedAt = new Date();
        await this.eventQueue.add(
          'author_pause_24h',
          { authorId: id },
          {
            delay: 24 * 60 * 60 * 1000,
            jobId: `author_pause_24h:${id}`,
          },
        );
      }
    }

    const author = await super.updateOne(req, dto);

    if (author.status != Status.Active) {
      await this.removeValueFromQueueRunning(author.id, author.parentId);
    }

    if (author.profileId) {
      this.multiLoginIntegrationService
        .changeProfileTiktok(author.profileId, author.uniqueId)
        .then(noop)
        .catch((err) =>
          this.logger.error(
            `Error changeProfileTiktok ${author.profileId} ${author.uniqueId}`,
            err,
          ),
        );
    }

    if (author.profileId) {
      const hasAtSuffix = author.profileId.endsWith('at');
      if (author.status === Status.Active && hasAtSuffix) {
        this.logger.debug(
          `Removing 'at' from profileId for author ${author.id}`,
        );
        let videos: Video[];
        await this.videoRepository.manager.transaction(
          async (transactionalEntityManager) => {
            const nextFetchVideoId = author.nextFetchVideoId || 0;

            // Fetch videos
            videos = await transactionalEntityManager
              .createQueryBuilder(Video, 'video')
              .where('video.author_id = :authorId', { authorId: author.id })
              .andWhere('video.status IN (:...status)', {
                status: [VideoStatus.InProgress, VideoStatus.Pending],
              })
              .andWhere('video.source_id::BIGINT <= :nextFetchVideoId', {
                nextFetchVideoId,
              })
              .select(['video.id', 'video.sourceId'])
              .getMany();

            const videoIds = videos.map((v) => v.id);

            // Update video status
            await transactionalEntityManager.update(
              Video,
              { id: In(videoIds) },
              { status: VideoStatus.Completed },
            );

            await transactionalEntityManager
              .createQueryBuilder(Author, 'author')
              .update()
              .where('id = :authorId', { authorId: author.id })
              .set({
                profileId: author.profileId.slice(0, -2),
              })
              .execute();
          },
        );
      } else if (author.status === Status.Manual && !hasAtSuffix) {
        this.logger.debug(`Adding 'at' to profileId for author ${author.id}`);
        await this.repo.update(author.id, {
          profileId: author.profileId + 'at',
        });
      }
      await author.reload();
    }

    return author;
  }

  async handleChangeProfile(oldId: string, newId: string) {
    this.logger.debug(
      `handleChangeProfile: ${JSON.stringify({ oldId, newId })}`,
    );
    const authors = await this.repo.findByIds([oldId, newId]);

    if (authors.length != 2) {
      this.throwNotFoundException(
        `Not found author with id ${oldId} or ${newId}`,
      );
    }

    const oldAuthor = authors.find((author) => author.id == oldId);
    const newAuthor = authors.find((author) => author.id == newId);

    if (!oldAuthor.profileId) {
      throw new ConflictException(`Old author ${oldId} not have profileId`);
    }

    if (newAuthor.profileId) {
      throw new ConflictException(
        `New author ${newId} already have profileId ${newAuthor.profileId}`,
      );
    }

    const profileId = oldAuthor.profileId;

    const needGenVideoBehavior =
      oldAuthor.sourceKind == SourceKind.ATGenVideo ||
      newAuthor.sourceKind == SourceKind.ATGenVideo;

    await this.repo.manager.transaction(async (transactionalEntityManager) => {
      const isFromTiktokToGenVideo =
        oldAuthor.sourceKind == SourceKind.Tiktok &&
        newAuthor.sourceKind == SourceKind.ATGenVideo;

      await transactionalEntityManager.update(Author, oldId, {
        profileId: null,
        ytbChannelId: null,
        profileEmail: null,
        accountId: null,
        parentId: null,
        ...(isFromTiktokToGenVideo && {
          status: Status.GenVideo,
        }),
      });

      await transactionalEntityManager.update(Author, newId, {
        profileId: profileId,
        accountId: oldAuthor.accountId,
        parentId: oldAuthor.parentId,
        ytbChannelId: null,
        profileEmail: null,
        ...(needGenVideoBehavior && {
          status: Status.Active,
        }),
      });

      await transactionalEntityManager.upsert(
        ProfileAuthor,
        [
          {
            profileId,
            id: generateSnowflakeId(),
            authorId: oldAuthor.id,
            isActive: false,
          },
          {
            profileId,
            id: generateSnowflakeId(),
            authorId: newAuthor.id,
            isActive: true,
          },
        ],
        ['profileId', 'authorId'],
      );
    });

    this._emitAlert('handleChangeProfile', { oldId, newId });
    await this.removeValueFromQueueRunning(oldAuthor.id, oldAuthor.parentId);

    // Execute the changeProfileTiktok function
    this.multiLoginIntegrationService
      .changeProfileTiktok(profileId, newAuthor.uniqueId)
      .then(noop)
      .catch((err) =>
        this.logger.error(
          `Error changeProfileTiktok ${profileId} ${newAuthor.uniqueId}`,
          err,
        ),
      );
    return { message: 'success' };
  }

  async getAuthorStatistical() {
    const stats = await this.repo
      .createQueryBuilder('author')
      .select([
        'COUNT(*) AS total',
        'COUNT(*) FILTER (WHERE author.profileId IS NOT NULL AND author.status != :copyrightStatus) AS allocated',
        'COUNT(*) FILTER (WHERE author.profileId IS NULL AND author.status != :copyrightStatus) AS unallocated',
        'COUNT(*) FILTER (WHERE author.profileId IS NULL AND author.isQualified AND author.status = :activeStatus) AS "unallocatedQualified"',
        'COUNT(*) FILTER (WHERE author.status = :copyrightStatus) AS copyright',
        'COUNT(*) FILTER (WHERE author.profileId IS NOT NULL AND author.status = :profileDiedStatus) AS "profileDied"',
        'COUNT(*) FILTER (WHERE author.profileId IS NOT NULL AND author.status = :activeStatus) AS "active"',
        'COUNT(*) FILTER (WHERE author.profileId IS NOT NULL AND author.source_kind = :genVideo) AS "totalGenVideo"',
      ])
      .setParameter('copyrightStatus', Status.Copyright)
      .setParameter('activeStatus', Status.Active)
      .setParameter('profileDiedStatus', Status.ProfileDied)
      .setParameter('genVideo', SourceKind.ATGenVideo)
      .getRawOne();

    return stats;
  }

  async assignChannel({
    profileId,
    accountId,
    parentId,
    countryCode,
    sourceKind,
  }: AssignChannel): Promise<{
    profileId: string;
    author: Author;
    accountId: string;
  }> {
    // default sourceKind to Tiktok if not provided
    if (!sourceKind) {
      sourceKind = SourceKind.Tiktok;
    }

    const existingAuthor = await this.repo.findOne({ profileId });

    if (existingAuthor) {
      if (existingAuthor.status != Status.Active) {
        throw new ConflictException(
          `Profile cannot be allocated to author; current status: ${existingAuthor.status}`,
        );
      }
      return {
        profileId,
        author: existingAuthor,
        accountId: existingAuthor.accountId,
      };
    }

    const author = await this.repo.findOne({
      profileId: IsNull(),
      status: Status.Active,
      sourceKind,
      ...(countryCode && { countryCode }),
    });

    !author && this.throwNotFoundException('There are currently no authors');

    author.profileId = profileId;
    author.accountId = accountId;

    parentId != undefined && (author.parentId = parentId);

    if (author.sourceKind == SourceKind.ATGenVideo) {
      author.status = Status.Inactive;
    }

    await this.repo.save(author);

    return { profileId, author, accountId };
  }

  async upsertAuthors(
    authorsData: {
      sourceId: string;
      nickname: string;
      uniqueId: string;
      sourceKind?: SourceKind;
    }[],
  ): Promise<Map<string, Author>> {
    // Step 1: Remove duplicates based on `sourceId`
    const uniqueAuthorsData = Array.from(
      new Map(authorsData.map((author) => [author.sourceId, author])).values(),
    );

    // Step 2: Insert data with conflict resolution
    const insertQuery = this.repo
      .createQueryBuilder()
      .insert()
      .values(
        uniqueAuthorsData.map((author) => ({
          ...author,
          status: Status.Inactive,
          id: generateSnowflakeId(),
        })),
      )
      .orUpdate(['unique_id'], ['source_id'])
      .returning('*');

    const upsertAuthors = await insertQuery.execute();

    // Step 3: Map the upserted authors to a Map
    const authorMap = new Map<string, Author>(
      upsertAuthors.generatedMaps.map((author: Author) => [
        author.sourceId,
        author,
      ]),
    );

    return authorMap;
  }

  async syncChannelMultipleLogin() {
    const authors = await this.repo.find({
      where: {
        profileId: Not(IsNull()),
        ytbChannelId: IsNull(),
      },
      select: ['id', 'profileId'],
    });

    const authorIds = authors.map((au) => au.profileId).filter(Boolean);

    const BATCH_SIZE = 50;
    const totalBatches = Math.ceil(authorIds.length / BATCH_SIZE);

    for (let i = 0; i < totalBatches; i++) {
      const batch = authorIds.slice(i * BATCH_SIZE, (i + 1) * BATCH_SIZE);

      const channels =
        await this.multiLoginIntegrationService.fetchChannelIdByProfileIds(
          batch,
        );

      if (channels.length) {
        // Construct the VALUES part dynamically
        const valuesSQL = channels
          .map(
            (channel) =>
              `('${channel.profileId}', '${channel.channelId}', '${channel.profileEmail}')`,
          )
          .join(', ');

        const query = `
        UPDATE author AS a
        SET ytb_channel_id = v.ytb_channel_id, profile_email = v.profile_email
        FROM (VALUES ${valuesSQL}) AS v(profile_id, ytb_channel_id, profile_email)
        WHERE a.profile_id = v.profile_id;
        `;

        await this.repo.query(query);
      }
    }
  }

  async toggleAuthorStatus(
    profileId: string,
    status: Status.Active | Status.Pause,
  ) {
    const author = await this.findOne({
      where: { profileId },
      select: ['id', 'profileId', 'status'],
    });
    !author &&
      this.throwNotFoundException(
        `Author with profileId ${profileId} not found`,
      );

    if (
      ![Status.Pause.valueOf(), Status.Active.valueOf()].includes(author.status)
    ) {
      throw new ConflictException(
        `Conflict flow, currently author status is: ${author.status}`,
      );
    }

    let pausedAt = null;

    if (status == Status.Pause) {
      pausedAt = new Date();

      await this.eventQueue.add(
        'author_pause_24h',
        { authorId: author.id },
        { delay: 24 * 60 * 60 * 1000, jobId: `author_pause_24h:${author.id}` },
      );
    }

    await this.repo.update(author.id, { status, pausedAt });

    return { message: 'success' };
  }

  async bulkModifyStatus(
    status:
      | Status.Active
      | Status.Copyright
      | Status.ProfileDied
      | Status.TiktokDied
      | Status.Inactive,
    profileIds: string[],
  ) {
    await this.repo.update(
      {
        profileId: In(profileIds),
      },
      { status },
    );

    const authors = await this.repo.find({
      where: { profileId: In(profileIds) },
      select: ['id', 'profileId', 'status', 'uniqueId', 'parentId'],
    });

    if (status != Status.Active) {
      for (const author of authors) {
        await this.removeValueFromQueueRunning(author.id, author.parentId);
      }
    }

    return { data: authors };
  }

  async updateAccountIdByEmail(
    email: string,
    accountId: string,
    parentId?: string,
  ) {
    console.log('updateAccountIdByEmail', { email, accountId, parentId });
    const author = await this.repo.findOne({ where: { profileEmail: email } });
    !author &&
      this.throwNotFoundException(`Author with email ${email} not found`);

    await this.repo.update(author.id, {
      accountId,
      ...(parentId != undefined && { parentId }),
    });

    return {
      id: author.id,
      profileId: author.profileId,
      accountId,
      status: author.status,
      uniqueId: author.uniqueId,
    };
  }

  async getAbsentAuthorsByDate(date: string): Promise<
    {
      id: string;
      accountId: string;
      profileId: string;
      uniqueId: string;
      isAbsent: boolean;
    }[]
  > {
    const authors = await this.repo
      .createQueryBuilder('author')
      .leftJoinAndSelect(
        'author.dailyChannelTraces',
        'dct',
        'dct.date = :date',
        {
          date,
        },
      )
      .andWhere('author.status = :activeStatus', {
        activeStatus: Status.Active,
      })
      .andWhere('author.profileId IS NOT NULL')
      .select([
        'author.id AS "id"',
        'author.accountId AS "accountId"',
        'author.profileId AS "profileId"',
        'author.uniqueId AS "uniqueId"',
        'dct.id IS NULL AS "isAbsent"',
      ])
      .getRawMany();

    return authors;
  }
}
