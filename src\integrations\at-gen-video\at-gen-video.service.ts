import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { ATGenVideoBaseResponse } from './dto';
import { retry } from '../../common/utils/retry.util';
import { ATVideos } from './dto/video';
import { plainToInstance } from 'class-transformer';
import { getRandomIntInclusive, sleep } from '../../common/utils';
import { detectLanguage } from '../../common/utils/language';

const AT_GEN_VIDEO_API_KEY = process.env.AT_GEN_VIDEO_API_KEY;
const AT_GEN_VIDEO_URL = process.env.AT_GEN_VIDEO_URL;

@Injectable()
export class ATGenVideoService {
  private logger = new Logger(ATGenVideoService.name);

  private retryConfig = {
    attempts: 3,
    delayMs: 5_000,
    exponential: true,
  };

  private _getBaseHeader() {
    return {
      baseURL: AT_GEN_VIDEO_URL,
      headers: {
        'x-api-key': AT_GEN_VIDEO_API_KEY,
      },
    };
  }

  async assignReup(reupId: string, atGenId: string) {
    this.logger.debug(`[assignReup] reupId=${reupId}, atGenId=${atGenId}`);

    await retry(
      async () => {
        await axios.patch(
          `/api/v1/internal/channels/${atGenId}/assign/${reupId}`,
          {},
          this._getBaseHeader(),
        );
      },
      this.retryConfig.attempts,
      this.retryConfig.delayMs,
      this.retryConfig.exponential,
    );
  }

  async fetchNewAuthor() {
    const authorIds = await retry(
      async () => {
        const response = await axios.get<string[]>(
          '/api/v1/internal/channels/new',
          this._getBaseHeader(),
        );

        return response.data;
      },
      this.retryConfig.attempts,
      this.retryConfig.delayMs,
      this.retryConfig.exponential,
    );

    return authorIds;
  }

  async fetchVideos(channelId: string, cursor?: string) {
    const params = new URLSearchParams();
    params.append('filter', `channelId||$eq||${channelId}`);
    cursor && params.append('filter', `id||$lt||${cursor}`);

    const videos = await retry(
      async () => {
        const response = await axios.get<ATGenVideoBaseResponse<ATVideos>>(
          '/api/v1/internal/videos',
          {
            params,
            ...this._getBaseHeader(),
          },
        );
        const baseInstance = plainToInstance(
          ATGenVideoBaseResponse<ATVideos>,
          response.data,
        );

        if (baseInstance && Array.isArray(baseInstance['data'])) {
          baseInstance['data'] = baseInstance['data'].map((item) =>
            plainToInstance(ATVideos, item),
          );
        }

        return baseInstance;
      },
      this.retryConfig.attempts,
      this.retryConfig.delayMs,
      this.retryConfig.exponential,
    );

    return videos;
  }

  async fetchAllVideos(
    channelId: string,
    stopId = '',
  ): Promise<ATGenVideoBaseResponse<ATVideos>[]> {
    let cursor: string | null = null;
    let hasMore = true;
    const results: ATGenVideoBaseResponse<ATVideos>[] = [];

    while (hasMore) {
      this.logger.debug(`Fetch Video ${channelId} ${cursor}`);
      const response = await this.fetchVideos(channelId, cursor);

      await sleep(getRandomIntInclusive(50, 200));

      results.push(response);

      if (
        stopId &&
        response.data &&
        response.data.some((video) => video.id == stopId)
      ) {
        this.logger.debug(`Stop ID ${stopId} found. Stopping fetch.`);
        break;
      }

      // Update cursor and hasMore
      hasMore = !!response?.data?.length;
      cursor = response?.data?.at(-1)?.id;
    }

    return results;
  }

  /**
   ** Language Requirement: The author primarily creates content in English (lang = 'en').
   ** Video Count Requirement: The author has published at least 100 videos.
   * @param authorSourceId
   * @returns
   */
  async isAuthorQualified(
    authorSourceId: string,
    amountVideo?: number,
  ): Promise<boolean> {
    const LANGUAGE = 'en';
    const MINIMUM_VIDEO_COUNT = amountVideo || 100;

    let cursor: string | null = null;
    let hasMore = true;
    const titles: string[] = [];

    while (hasMore) {
      this.logger.debug(`Fetch Video ${authorSourceId} ${cursor}`);
      const response = await this.fetchVideos(authorSourceId, cursor);

      await sleep(getRandomIntInclusive(50, 200));

      // Store the current batch of results
      titles.push(...response.data.map((item) => item.subTask.videoScript));

      if (titles.length >= MINIMUM_VIDEO_COUNT) {
        break;
      }

      // Update cursor and hasMore
      hasMore = !!response?.data?.length;
      cursor = response?.data?.at(-1)?.id;
    }

    // ---------- 'bypass check language check for GenVideo, because feature/product-fusion' ----
    return titles.length >= MINIMUM_VIDEO_COUNT;

    // return (
    //   titles.length >= MINIMUM_VIDEO_COUNT &&
    //   detectLanguage(titles.filter((x) => !!x)) == LANGUAGE
    // );
  }
}
