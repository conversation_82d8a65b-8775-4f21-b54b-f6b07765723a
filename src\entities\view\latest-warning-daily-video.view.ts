import {
  <PERSON>Entity,
  ViewColumn,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PrimaryColumn,
} from 'typeorm';
import { Author } from '../author';

@ViewEntity({
  expression: `
    WITH video_data AS (
        SELECT
            dct.id,
            dct.author_id,
            dct.video_ids,
            COUNT(*) FILTER (WHERE v.status = 'completed')::int AS completed_count,
            COUNT(*) FILTER (WHERE v.status = 'error')::int AS error_count,
            dct.total_allocations,
            dct."date"::varchar,
            dct.reserved_at
        FROM daily_channel_trace dct
        CROSS JOIN LATERAL (
          SELECT DISTINCT vid FROM unnest(dct.video_ids::BIGINT[]) AS vid(vid)
        ) AS unique_vids
        LEFT JOIN video v ON v.id = unique_vids.vid::BIGINT
        WHERE dct."date" = (SELECT MAX("date") FROM daily_channel_trace)
        GROUP BY dct.id, dct.author_id, dct.video_ids, dct.total_allocations, dct.reserved_at
    )
    SELECT
      vd.id, vd.author_id, vd.video_ids,
      vd.completed_count, vd.total_allocations, vd."date",
      vd.error_count
    FROM video_data vd INNER JOIN author a on vd.author_id = a.id
    WHERE
      a.status = 'active'
    AND (
        EXISTS (
            SELECT 1
            FROM video v
            CROSS JOIN LATERAL unnest(vd.video_ids) AS vid
            WHERE v.id = vid::BIGINT
            AND v.status = 'in_progress'
            AND v.assigned_at < NOW() - INTERVAL '60 MINUTE'
        )
        OR (vd.completed_count + vd.error_count >= vd.total_allocations AND vd.completed_count < vd.total_allocations)
    )
    AND (
        -- Use reservedAt instead of reassignHistory
        vd.reserved_at IS NULL
        OR vd.reserved_at < NOW() - INTERVAL '60 MINUTE'
    );`,
})
export class LatestWarningDailyVideoView {
  @PrimaryColumn()
  id: string;

  @ViewColumn()
  authorId: string;

  @ViewColumn()
  videoIds: string[];

  @ViewColumn()
  completedCount: number;

  @ViewColumn()
  error_count: number;

  @ViewColumn()
  totalAllocations: number;

  @ViewColumn()
  date: string;

  @ManyToOne(() => Author, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'author_id', referencedColumnName: 'id' })
  author: Author;
}
