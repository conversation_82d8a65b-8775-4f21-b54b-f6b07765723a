name: reup-tiktok

services:
  postgres:
    image: postgres:latest
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: reup-tiktok
    volumes:
      - postgres_data:/var/lib/postgresql/data
    shm_size: 2g
    command: |
      postgres
      -c shared_buffers=2GB
      -c work_mem=64MB
      -c maintenance_work_mem=512MB
      -c effective_cache_size=6GB
      -c max_parallel_workers=4
      -c max_parallel_workers_per_gather=2
      -c max_connections=100
      -c log_min_duration_statement=5000

  redis:
    image: redis:alpine
    command: ['redis-server', '--notify-keyspace-events', 'Ex']

  backend:
    image: reup-tiktok-core
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env
    environment:
      - DB_URL=******************************************/reup-tiktok
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    deploy:
      replicas: 1
      restart_policy:
        condition: any
    healthcheck:
      test: wget --no-verbose --tries=1 --spider http://0.0.0.0:3210/api/v1/health/ping || exit 1
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 1m
    logging:
      driver: 'json-file'
      options:
        max-size: '20m'
        max-file: '3'

  worker:
    image: reup-tiktok-core
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env
    environment:
      - DB_URL=******************************************/reup-tiktok
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    deploy:
      replicas: 1
      restart_policy:
        condition: any
    command: ['node', 'dist/worker.js']
    logging:
      driver: 'json-file'
      options:
        max-size: '20m'
        max-file: '3'

  nginx:
    image: nginx:latest
    container_name: nginx_load_balancer
    ports:
      - '13210:80'
      - '15432:5432'
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
      - redis
      - postgres
    logging:
      driver: 'json-file'
      options:
        max-size: '20m'
        max-file: '3'
    deploy:
      restart_policy:
        condition: any

volumes:
  postgres_data:
