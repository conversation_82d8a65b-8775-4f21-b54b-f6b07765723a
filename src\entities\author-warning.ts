import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { Column, Entity } from 'typeorm';
import { WithIdAndTimestamp } from './withIdAndTimestamp';

@Entity('author_warning')
export class AuthorWarning extends WithIdAndTimestamp {
  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  authorId: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  profileId: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  uniqueId: string;

  @ApiProperty()
  @IsString()
  @Column({ nullable: true })
  reason: string;
}
