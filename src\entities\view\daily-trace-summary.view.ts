import { ViewEntity, ViewColumn } from 'typeorm';

@ViewEntity({
  expression: `
    WITH daily_trace_summary AS (
        SELECT
            "date",
            COUNT(*) AS trace_count,
            SUM(total_allocations) AS total_allocations
        FROM
            daily_channel_trace
        WHERE
            "date" >= CURRENT_DATE - INTERVAL '9 days'
        GROUP BY
            "date"
    )
    SELECT
        d."date"::varchar,
        d.trace_count,
        d.total_allocations,
        COUNT(v.id) FILTER (WHERE v.status = 'pending') AS pending,
        COUNT(v.id) FILTER (WHERE v.status = 'in_progress') AS in_progress,
        COUNT(v.id) FILTER (WHERE v.status = 'completed') AS completed,
        COUNT(v.id) FILTER (WHERE v.status = 'error') AS error,
        COUNT(v.id) AS total_videos
    FROM
        daily_trace_summary d
    LEFT JOIN
        LATERAL (
            SELECT
                DISTINCT UNNEST(video_ids) AS video_id
            FROM
                daily_channel_trace
            WHERE
                "date" = d."date"
        ) trace_videos ON TRUE
    LEFT JOIN
        video v ON v.id = trace_videos.video_id::BIGINT
    GROUP BY
        d."date", d.trace_count, d.total_allocations`,
})
export class DailyTraceSummaryView {
  @ViewColumn()
  date: string;

  @ViewColumn()
  traceCount: number;

  @ViewColumn()
  totalAllocations: number;

  @ViewColumn()
  pending: number;

  @ViewColumn()
  inProgress: number;

  @ViewColumn()
  completed: number;

  @ViewColumn()
  error: number;

  @ViewColumn()
  totalVideos: number;
}
