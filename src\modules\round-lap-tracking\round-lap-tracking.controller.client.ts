import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { RoundLapTracking } from '../../entities';
import { RoundLapTrackingService } from './round-lap-tracking.service';

@Auth()
@Crud({
  model: {
    type: RoundLapTracking,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
    /*    join: {
      colorCombinations: { eager: false },
    }, */
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('client/round-lap-trackings')
@Controller('client/round-lap-trackings')
export class ClientRoundLapTrackingController
  implements CrudController<RoundLapTracking>
{
  constructor(public service: RoundLapTrackingService) {}
}
