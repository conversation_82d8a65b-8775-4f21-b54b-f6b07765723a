import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { Innertube, UniversalCache } from 'youtubei.js';
import { parseViewCount } from '../../../common/utils/ytb.helper';
import { ShortVideo } from '../../../entities';
import { ShortVideoService } from '../short-video.service';
import { sleep } from '../../../common/utils';

const CONCURRENCY = parseInt(process.env.WORKER_CONCURRENCY) || 10;

@Processor('short-video', { concurrency: CONCURRENCY })
export class ShortVideoProcessor extends WorkerHost {
  private logger = new Logger(ShortVideoProcessor.name);

  constructor(private shortVideoService: ShortVideoService) {
    super();
  }

  private async fetchYtbNewSession() {
    return Innertube.create({
      cache: new UniversalCache(false),
    });
  }

  async process(
    job: Job<{ videoIds: string[] }>,
    token?: string,
  ): Promise<any> {
    const ytb = await this.fetchYtbNewSession();
    const BATCH_SIZE = 20;
    const shorts = job.data.videoIds;
    let results = [];
    // Split the IDs into chunks of BATCH_SIZE
    for (let i = 0; i < shorts.length; i += BATCH_SIZE) {
      await job.log(`Crawl info: ${i}/${shorts.length}`);
      const batch = shorts.slice(i, i + BATCH_SIZE);
      const batchResults = await Promise.allSettled(
        batch.map((videoId) => this.fetchShortVideoInfo(videoId, ytb)),
      );
      await sleep(1_000);
      results = results.concat(
        batchResults
          .filter(
            (item): item is PromiseFulfilledResult<ShortVideo> =>
              item.status == 'fulfilled',
          )
          .map((x) => x.value),
      );
    }

    await this.shortVideoService.upsertShortVideo(results);
  }

  private async fetchShortVideoInfo(
    videoId: string,
    ytb: Innertube,
  ): Promise<ShortVideo> {
    const videoInfo = await ytb.getInfo(videoId);
    const ytbChannelId =
      videoInfo.basic_info?.channel?.id ||
      videoInfo.secondary_info?.owner?.author?.id;

    return {
      videoId,
      description:
        videoInfo.basic_info?.short_description ||
        videoInfo.secondary_info?.description?.runs
          ?.map((item) => item.text)
          .join(''),
      thumbnail: videoInfo.basic_info?.thumbnail?.at(0)?.url,
      viewCount:
        videoInfo.basic_info?.view_count ||
        parseViewCount(videoInfo.primary_info.view_count.view_count?.text),
      likeCount:
        videoInfo.basic_info?.like_count ||
        videoInfo.primary_info.menu.top_level_buttons.find(
          (button) => button.type == 'SegmentedLikeDislikeButtonView',
        )['like_count'] ||
        null,
      title: videoInfo.basic_info?.title || videoInfo.primary_info.title.text,
      ytbChannelId,
      duration: videoInfo.basic_info?.duration,
      published: videoInfo.primary_info.published.text,
    } as unknown as ShortVideo;
  }
}
