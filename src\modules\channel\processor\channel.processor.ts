import { InjectQueue, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job, Queue } from 'bullmq';

import { Innertube, UniversalCache } from 'youtubei.js';
import {
  AboutChannel,
  ShortsLockupView,
} from 'youtubei.js/dist/src/parser/nodes';
import { Channel } from 'youtubei.js/dist/src/parser/youtube';
import {
  parseViewCount,
  sanitizeString,
} from '../../../common/utils/ytb.helper';
import { ChannelService } from '../channel.service';
import { Channel as ChannelEntity } from '../../../entities';
import { DeepPartial } from 'typeorm';

const CONCURRENCY = parseInt(process.env.WORKER_CONCURRENCY) || 10;

@Processor('channel', { concurrency: CONCURRENCY })
export class ChannelProcessor extends WorkerHost {
  private logger = new Logger(ChannelProcessor.name);

  constructor(
    @InjectQueue('short-video') private shortVideoQueue: Queue,
    private channelService: ChannelService,
  ) {
    super();
  }

  private async fetchYtbNewSession() {
    return Innertube.create({
      cache: new UniversalCache(false),
    });
  }

  async process(job: Job<{ channelId: string }>, token?: string): Promise<any> {
    // this.logger.log(job);
    // return;
    const ytb = await this.fetchYtbNewSession();

    // Step 1: fetch channelInfo
    await job.log('[Step 1]: fetch channelInfo');
    const channel = await ytb.getChannel(job.data.channelId);
    const channelInfo = await this.fetchChannelInfo(channel);

    await this.channelService.upsertChannel(channelInfo);

    // Step 2: fetch all short video id in this channel
    await job.log('Step 2: fetch all short video id in this channel');
    const shortVideoUniqueId = new Set<string>();

    let videos = await channel.getShorts();
    for (const video of videos.videos) {
      shortVideoUniqueId.add(
        (video as ShortsLockupView).on_tap_endpoint.payload.videoId,
      );
    }

    let count = 0;
    while (videos.has_continuation) {
      count++;
      await job.log(
        `[Continuation]: ${count} => Size: ${shortVideoUniqueId.size}`,
      );
      if (count > 50_000) {
        break;
      }

      videos = (await videos.getContinuation()) as unknown as Channel;

      videos.videos.forEach((video) => {
        shortVideoUniqueId.add(
          (video as ShortsLockupView).on_tap_endpoint.payload.videoId,
        );
      });
    }

    await job.log('[Final]: publish job fetch short video info');

    const BATCH_SIZE = 500;

    // Convert Set to Array
    const videoIds = Array.from(shortVideoUniqueId);

    const totalBatches = Math.ceil(videoIds.length / BATCH_SIZE);

    for (let i = 0; i < totalBatches; i++) {
      const batch = videoIds.slice(i * BATCH_SIZE, (i + 1) * BATCH_SIZE);

      await this.shortVideoQueue.add('fetchShortVideos', { videoIds: batch });
    }

    await job.log(
      `[Final]: Published ${videoIds.length} videos in ${totalBatches} batches`,
    );
  }

  private async fetchChannelInfo(
    channel: Channel,
  ): Promise<DeepPartial<ChannelEntity>> {
    const basicInfo = channel.metadata;
    const about = (await channel.getAbout()) as AboutChannel;
    return {
      title: basicInfo.title,
      description: sanitizeString(about.metadata.description),
      thumbnail: basicInfo.thumbnail.at(0)?.url,
      avatar: basicInfo.avatar.at(0)?.url,
      url: basicInfo.url,
      vanityChannelUrl: basicInfo.vanity_channel_url,
      country: about.metadata.country,
      joinedDate: new Date(about.metadata.joined_date?.toString()),
      ytbChannelId: about.metadata.channel_id,
      viewCount: parseViewCount(about.metadata.view_count)?.toString(),
      subscriberCount: parseViewCount(
        about.metadata.subscriber_count,
      )?.toString(),
      videoCount: parseViewCount(about.metadata.video_count)?.toString(),
      tags: basicInfo.tags,
    };
  }
}
