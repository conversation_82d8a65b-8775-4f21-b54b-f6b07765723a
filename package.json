{"name": "base-project", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"generate": "plop", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc -p tsconfig.build.json", "prebuild:prod": "<PERSON><PERSON><PERSON> dist", "build:prod": "tsc -p tsconfig.build.json", "postbuild:prod": "copyfiles --up 1 src/**/*.json dist", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "worker:dev": "nest start --config nest-cli.worker.json --watch", "worker:prod": "node dist/worker", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@bull-board/api": "^6.5.4", "@bull-board/express": "^6.5.4", "@nestjs/bull": "^0.6.3", "@nestjs/bullmq": "^10.2.3", "@nestjs/cli": "^9.2.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.0", "@nestjs/core": "^9.0.0", "@nestjs/event-emitter": "^2.0.4", "@nestjs/jwt": "^10.0.1", "@nestjs/microservices": "9.4.3", "@nestjs/passport": "^9.0.1", "@nestjs/platform-express": "^9.0.0", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^6.2.1", "@nestjs/typeorm": "^9.0.1", "@nestjsx/crud": "^5.0.0-alpha.3", "@nestjsx/crud-typeorm": "^5.0.0-alpha.3", "@supercharge/request-ip": "^1.2.0", "@type-cacheable/core": "^11.1.2", "aws-sdk": "^2.1334.0", "axios": "^1.7.9", "bcrypt": "^5.1.0", "bull": "^4.10.4", "bullmq": "^5.34.6", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compare-versions": "^6.0.0-rc.1", "dotenv": "^16.0.3", "exceljs": "^4.4.0", "express-basic-auth": "^1.2.1", "fast-csv": "^5.0.2", "googleapis": "^148.0.0", "https-proxy-agent": "^7.0.6", "ioredis": "^5.3.1", "kafkajs": "^2.2.4", "langdetect": "^0.2.1", "minio": "^8.0.3", "nodejs-snowflake": "^2.0.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "pg": "^8.9.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "slug": "^8.2.2", "typeorm": "0.2.45", "typeorm-naming-strategies": "^4.1.0", "uuid": "^10.0.0", "youtubei.js": "^13.0.0"}, "devDependencies": {"@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/cron": "^2.4.3", "@types/express": "^4.17.13", "@types/jest": "28.1.4", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "copyfiles": "^2.4.1", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "28.1.2", "plop": "3.1.2", "pluralize": "^8.0.0", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}