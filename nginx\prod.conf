events {}

http {
    server_tokens off;
    resolver 127.0.0.11 valid=10s;  # Docker DNS with re-query every 10 seconds
    client_max_body_size 50M;

    # Default or fallback server
    server {
        listen 80 default_server;

        location / {
            return 404;
        }
    }

# =====================================================================
    server {
        listen 80;
        server_name *.atechdigital.space;
        return 301 https://$host$request_uri;
    }

    # =============== BACKEND SERVER ===============
    upstream backend_servers {
        server backend:3210;
    }

    server {
        listen 443 ssl;
        server_name api-reup-tiktok.atechdigital.space;

        ssl_certificate /etc/letsencrypt/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/privkey.pem;

        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        location / {
            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 120s;  
            proxy_read_timeout 300s;   
            proxy_send_timeout 300s;   
        }
    }
    # =============== SUMMARY SERVERS ===============
    upstream summary_servers {
        server summary_backend:8000;
    }

    server {
        listen 443 ssl;
        server_name api-summary.atechdigital.space;

        ssl_certificate /etc/letsencrypt/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/privkey.pem;

        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        location / {
            proxy_pass http://summary_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_read_timeout 300s;    
        }
    }
    # =============== Frontend ===============
    upstream fe_servers {
        server reup-tiktok-fe:80;
    }

    server {
        listen 443 ssl;
        server_name reup-tiktok.atechdigital.space;

        ssl_certificate /etc/letsencrypt/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/privkey.pem;

        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        location / {
            proxy_pass http://fe_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
