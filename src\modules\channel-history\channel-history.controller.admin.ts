import { Controller } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { <PERSON><PERSON>, CrudController } from '@nestjsx/crud';

import { Auth } from '../../decorators';
import { ChannelHistory } from '../../entities';
import { ChannelHistoryService } from './channel-history.service';

@Auth()
@Crud({
  model: {
    type: ChannelHistory,
  },
  query: {
    alwaysPaginate: true,
    softDelete: true,
    maxLimit: 100,
  },
  routes: {
    only: [
      'getOneBase',
      'getManyBase',
      'createOneBase',
      'updateOneBase',
      'deleteOneBase',
    ],
    updateOneBase: {
      allowParamsOverride: true,
      returnShallow: true,
    },
  },
})
@ApiTags('admin/channel-histories')
@Controller('admin/channel-histories')
export class AdminChannelHistoryController
  implements CrudController<ChannelHistory>
{
  constructor(public service: ChannelHistoryService) {}
}
