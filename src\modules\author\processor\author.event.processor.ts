import { InjectQueue, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job, Queue } from 'bullmq';
import { AuthorService } from '../author.service';
import { Status } from '../../../common/enum/status.enum';

const CONCURRENCY = parseInt(process.env.WORKER_CONCURRENCY) || 10;

@Processor('author-event', { concurrency: CONCURRENCY })
export class AuthorEventProcessor extends WorkerHost {
  private logger = new Logger(AuthorEventProcessor.name);

  constructor(
    public authorService: AuthorService,

    @InjectQueue('dlq') private dlqQueue: Queue,
  ) {
    super();
  }

  async process(job: Job<{ authorId: string }>, token?: string): Promise<any> {
    this.logger.debug(`JobName: ${job.name}`);
    if ('author_pause_24h' == job.name) {
      await this.authorService.repo.update(
        {
          id: job.data.authorId,
          status: Status.Pause,
        },
        { status: Status.Active, pausedAt: null },
      );
      return;
    }
  }
}
