import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { DecimalTransformer } from '../common/transformer';
import { Author } from './author';
import { WithIdAndTimestamp } from './withIdAndTimestamp';
import { TransformTimestamp } from '../decorators/transform-timestamp.decorator';
import { YoutubeMetadata } from '../modules/task/request/youtube-metadata.dto';

export enum VideoStatus {
  Pending = 'pending',
  InProgress = 'in_progress',
  Completed = 'completed',
  Error = 'error',
}

@Entity('video')
@Index('idx_video_author_id_status', [
  'authorId',
  'status',
  'deletedAt',
  'sourceId',
])
export class Video extends WithIdAndTimestamp {
  @ApiProperty()
  @IsString()
  @Column({ unique: true })
  sourceId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Column({ nullable: true })
  description: string;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  cover: string;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  followerCount: number;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  diggCount: number;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  shareCount: number;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  videoCount: number;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  playCount: number;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  commentCount: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @Column('numeric', {
    precision: 10,
    scale: 2,
    transformer: new DecimalTransformer(),
    nullable: true,
  })
  interactionRate: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @Column('numeric', {
    precision: 10,
    scale: 2,
    transformer: new DecimalTransformer(),
    nullable: true,
  })
  conversionRate: number;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  totalSalesVolume: number;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  totalSalesAmount: string;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  totalSalesAmountUsd: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @Column('numeric', {
    precision: 10,
    scale: 2,
    transformer: new DecimalTransformer(),
    nullable: true,
  })
  avgPrice: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @Column('numeric', {
    precision: 10,
    scale: 2,
    transformer: new DecimalTransformer(),
    nullable: true,
  })
  avgPriceUsd: number;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  duration: number;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  video: string;

  @ApiProperty()
  @IsOptional()
  @Column({ type: 'jsonb', nullable: true })
  tags: any;

  @ApiProperty()
  @IsOptional()
  @Column({ type: 'jsonb', nullable: true })
  music: any;

  @ApiProperty()
  @IsOptional()
  @Column({ type: 'jsonb', nullable: true })
  snapshot: any;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true, default: false })
  isAd: boolean;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  isCommerce: boolean;

  @ApiProperty()
  @IsOptional()
  @TransformTimestamp()
  @Type(() => Date)
  @Column({ nullable: true })
  createTime: Date;

  @ApiProperty()
  @IsOptional()
  @TransformTimestamp()
  @Type(() => Date)
  @Column({ nullable: true })
  lastTime: Date;

  @ApiProperty()
  @IsOptional()
  @Column({ nullable: true })
  countryCode: string;

  @ManyToOne(() => Author, (author) => author.videos)
  author: Author;

  @Column()
  authorId: string;

  @ApiProperty()
  @IsEnum(VideoStatus)
  @Column({ default: VideoStatus.Pending })
  status: string;

  @Column({ nullable: true })
  assignedAt: Date;

  @Column({ nullable: true })
  completedAt: Date;

  @Column({ nullable: true, type: 'jsonb' })
  youtubeMetadata: YoutubeMetadata;

  @Column({ nullable: true })
  videoUrl: string;

  @Column({ nullable: true })
  title: string;

  @Column({ nullable: true })
  videoDriveUrl: string;
}
