import { ApiProperty } from '@nestjs/swagger';
import { IsString, Matches } from 'class-validator';
import { STRONG_PASSWORD_REGEX } from '../../../common/validator/regex';

export class ChangePasswordDto {
  @ApiProperty()
  @IsString()
  password: string;

  @IsString()
  @ApiProperty({ minLength: 8 })
  @Matches(STRONG_PASSWORD_REGEX, { message: 'newPassword not strong' })
  newPassword: string;
}
