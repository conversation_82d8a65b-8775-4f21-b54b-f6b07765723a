import { Column, Entity } from 'typeorm';
import { WithIdAndTimestamp } from './withIdAndTimestamp';
import { AUDIT_TYPE } from '../common/enum/audit.enum';

@Entity('audit')
export class Audit extends WithIdAndTimestamp {
  @Column('bigint')
  userId: string;

  @Column({ nullable: true })
  ip: string;

  @Column('jsonb', { nullable: true })
  headers: object | null;

  @Column('character varying', { default: AUDIT_TYPE.LOGIN })
  type: AUDIT_TYPE;
}
