import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { Proxy } from '../../entities';
import { Repository } from 'typeorm';

@Injectable()
export class ProxyService extends TypeOrmCrudService<Proxy> {
  constructor(@InjectRepository(Proxy) repo: Repository<Proxy>) {
    super(repo);
  }

  async getRandomProxy(): Promise<Proxy | null> {
    const builder = this.repo
      .createQueryBuilder('p')
      .andWhere('p.isActive = true');

    const totalProxies = await builder.clone().getCount();
    if (!totalProxies) {
      return null;
    }

    const randomOffset = Math.floor(Math.random() * totalProxies);
    return builder.clone().offset(randomOffset).limit(1).getOne();
  }

  async deleteBulk(ids: string[]) {
    await this.repo.delete(ids);
    return { message: 'success' };
  }
}
