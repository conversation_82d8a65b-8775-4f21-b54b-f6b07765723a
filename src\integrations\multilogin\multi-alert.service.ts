import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

const TELEGRAM_BOT_TOKEN = process.env.MULTI_TELEGRAM_BOT_TOKEN;
const TELEGRAM_CHAT_ID = process.env.MULTI_TELEGRAM_CHAT_ID;

export const TELEGRAM_THREAD = {
  tiktok: '3',
};

@Injectable()
export class MultiLoginAlertService {
  private logger = new Logger(MultiLoginAlertService.name);

  async sendMessage(messages: string[]) {
    await axios({
      method: 'GET',
      url: `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage`,
      params: {
        chat_id: TELEGRAM_CHAT_ID,
        text: messages.join('\n'),
        message_thread_id: TELEGRAM_THREAD.tiktok,
        parse_mode: 'Markdown',
      },
    });
  }
}
