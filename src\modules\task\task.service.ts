import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, In, Repository } from 'typeorm';

import { Status } from '../../common/enum/status.enum';
import { getRandomIntInclusive } from '../../common/utils';
import { getVNDayRange } from '../../common/utils/date.util';
import { generateSnowflakeId } from '../../common/utils/snowflake.util';
import { removePrefixAndCamelCase } from '../../common/utils/string.util';
import { Author, DailyChannelTrace, Video, VideoStatus } from '../../entities';
import { DailyChannelTraceService } from '../daily-channel-trace/daily-channel-trace.service';
import { SettingService } from '../setting/setting.service';
import { YoutubeMetadata } from './request/youtube-metadata.dto';

@Injectable()
export class TaskService {
  constructor(
    @InjectRepository(Video)
    private readonly videoRepository: Repository<Video>,
    @InjectRepository(Author)
    private readonly authorRepository: Repository<Author>,
    private readonly settingService: SettingService,
    private readonly dailyChannelTraceService: DailyChannelTraceService,
  ) {}

  async updateAuthorLastAssignedAt(authorId: any) {
    return this.authorRepository.update(
      { id: authorId },
      { lastAssignedAt: new Date() },
    );
  }

  /**
   * A video task is only assigned if:
    - All prior videos in the author are completed.
    - The rest time for the last completed video has elapsed.
    - The daily limit of 3 videos for the author is not exceeded.
   * @returns
   */
  async getNextTask(): Promise<{ profileId: string; videos: Video[] }> {
    const { startOfDayUTC: startOfDay, startOfDayFormatted } = getVNDayRange();

    const eligibleVideoStatuses = [VideoStatus.InProgress, VideoStatus.Pending];

    let result = null;

    // Start a transaction and ensure all operations are within this transaction
    await this.videoRepository.manager.transaction(
      // 'SERIALIZABLE',
      async (transactionalEntityManager) => {
        // Step 1: Select a random author
        const queryBuilder = await transactionalEntityManager
          .createQueryBuilder(Author, 'author')
          .innerJoin(
            Video,
            'video',
            'author.id = video.authorId AND video.status IN (:...status)' +
              ' AND COALESCE("author"."next_fetch_video_id"::BIGINT, 0::BIGINT) <= "video"."source_id"::BIGINT',
            { status: eligibleVideoStatuses },
          )
          .select('author.id')
          .addSelect('author.nickname')
          .addSelect('author.uniqueId')
          .addSelect('author.sourceId')
          .addSelect('author.profileId')
          .addSelect('author.nextFetchVideoId')
          .addSelect('author.dailyUploadLimitRange')
          .andWhere(
            new Brackets((qb) => {
              qb.orWhere('author.lastAssignedAt IS NULL');
              qb.orWhere('author.lastAssignedAt < :startOfDay', { startOfDay });
            }),
          )
          .andWhere('author.profileId IS NOT NULL')
          .andWhere('author.status = :authorStatus', {
            authorStatus: Status.Active,
          })
          .orderBy('author.priority', 'ASC', 'NULLS LAST')
          .addOrderBy('author.createdAt', 'DESC', 'NULLS LAST')
          .limit(1);

        // Add `FOR UPDATE SKIP LOCKED` manually
        const sql = queryBuilder.getSql() + ' FOR UPDATE SKIP LOCKED';
        const parameters = [
          ...eligibleVideoStatuses,
          startOfDay,
          Status.Active,
        ];

        const rawResults = await transactionalEntityManager.query(
          sql,
          parameters,
        );
        const author = removePrefixAndCamelCase(
          rawResults[0],
          'author_',
        ) as Author;

        if (!author) {
          throw new NotFoundException('No tasks available');
        }

        await transactionalEntityManager.update(
          Author,
          { id: author.id },
          { lastAssignedAt: new Date() },
        );

        // Step 1.1: get dailyUploadLimitRange
        let dailyUploadLimitRange = author.dailyUploadLimitRange;
        if (!dailyUploadLimitRange) {
          dailyUploadLimitRange =
            await this.settingService.getDailyUploadLimitRange();
        }

        // Step 2: Select a random video within the range [min, max]
        const randomRange = getRandomIntInclusive(
          dailyUploadLimitRange.min,
          dailyUploadLimitRange.max,
        );

        const nextFetchVideoId = author.nextFetchVideoId || 0;

        const videos = await transactionalEntityManager
          .createQueryBuilder(Video, 'video')
          .where('video.author_id = :authorId', {
            authorId: author.id,
          })
          .andWhere('video.status IN (:...status)', {
            status: eligibleVideoStatuses,
          })
          .andWhere('video.source_id::BIGINT > :nextFetchVideoId', {
            nextFetchVideoId,
          })
          .orderBy('video.source_id::BIGINT')
          .limit(randomRange)
          .select([
            'video.id',
            'video.sourceId',
            'video.description',
            'video.cover',
            'video.video',
          ])
          .getMany();

        if (!videos) {
          throw new NotFoundException('No tasks available');
        }

        const videoIds = videos.map((video) => video.id);

        await transactionalEntityManager.update(
          Video,
          { id: In(videoIds) },
          { status: VideoStatus.InProgress, assignedAt: new Date() },
        );

        await transactionalEntityManager.insert(DailyChannelTrace, {
          id: generateSnowflakeId(),
          authorId: author.id,
          date: startOfDayFormatted,
          videoIds: videoIds,
          totalAllocations: videoIds.length,
        });

        const uploadTimeConfig =
          await this.settingService.getUploadTimeConfig();
        result = { author, videos, dailyUploadLimitRange, uploadTimeConfig };
      },
    );

    return result;
  }

  async confirmVideoTaskCompletion(
    videoId: string,
    youtubeMetadata: YoutubeMetadata,
  ) {
    const task = await this.videoRepository.findOne(videoId);

    if (!task || task.status !== VideoStatus.InProgress) {
      throw new NotFoundException('Video not found or not in progress');
    }

    await this.videoRepository.update(task.id, {
      status: VideoStatus.Completed,
      completedAt: new Date(),
      youtubeMetadata,
    });

    // await this.dailyChannelTraceService.updateDetailCount(
    //   task.authorId,
    //   'successfulCount',
    // );
    return { message: 'success' };
  }

  async confirmVideoTaskError(
    videoId: string,
    youtubeMetadata: YoutubeMetadata,
  ) {
    const task = await this.videoRepository.findOne(videoId);

    if (!task || task.status !== VideoStatus.InProgress) {
      throw new NotFoundException('Video not found or not in progress');
    }

    await this.videoRepository.update(task.id, {
      status: VideoStatus.Error,
      completedAt: new Date(),
      youtubeMetadata,
    });

    // await this.dailyChannelTraceService.updateDetailCount(
    //   task.authorId,
    //   'failedCount',
    // );
    return { message: 'success' };
  }

  // async recoverStaleTasks(): Promise<void> {
  //   const staleThreshold = new Date();
  //   staleThreshold.setMinutes(staleThreshold.getMinutes() - 10); // Example: 10 minutes timeout

  //   const staleTasks = await this.videoRepository.find({
  //     where: {
  //       status: 'in_progress',
  //       assigned_at: LessThanOrEqual(staleThreshold),
  //     },
  //   });

  //   for (const task of staleTasks) {
  //     task.status = 'pending';
  //     task.assignedAt = null; // Reset assignment
  //     await this.videoRepository.save(task);
  //   }
  // }

  async getNextTaskTest(
    id: string,
  ): Promise<{ profileId: string; videos: Video[] }> {
    // Check author allocation schedule today
    const allocation = await this.dailyChannelTraceService.getTodayAllocation(
      id,
    );
    if (!allocation) {
      throw new ConflictException('No schedule found for the author today');
    }

    // Query total error/completed videos for today
    const totalCompleted = await this.videoRepository.count({
      where: {
        id: In(Array.from(new Set(allocation.videoIds))),
        status: VideoStatus.Completed,
      },
    });

    if (totalCompleted >= allocation.totalAllocations) {
      throw new ConflictException(
        'All videos for this author have already been allocated and completed for today.',
      );
    }

    const totalReassign = allocation.totalAllocations - totalCompleted;

    const eligibleVideoStatuses = [VideoStatus.InProgress, VideoStatus.Pending];

    let result = null;
    // Start a transaction and ensure all operations are within this transaction
    await this.videoRepository.manager.transaction(
      async (transactionalEntityManager) => {
        // Step 1: Select a random author
        const author = await transactionalEntityManager
          .createQueryBuilder(Author, 'author')
          .setLock('pessimistic_write')
          .innerJoin(
            Video,
            'video',
            'author.id = video.authorId AND video.status IN(:...status) ',
            { status: eligibleVideoStatuses },
          )
          .select('author.id')
          .addSelect('author.nickname')
          .addSelect('author.uniqueId')
          .addSelect('author.sourceId')
          .addSelect('author.profileId')
          .addSelect('author.nextFetchVideoId')
          .addSelect('author.dailyUploadLimitRange')
          .addSelect('author.sourceKind')
          .andWhere('author.id = :id', { id })
          .andWhere('author.profileId IS NOT NULL')
          .andWhere('author.status = :authorStatus', {
            authorStatus: Status.Active,
          })
          .orderBy('author.priority', 'ASC', 'NULLS LAST')
          .limit(1)
          .getOne();

        if (!author) {
          throw new NotFoundException('No tasks available');
        }

        await transactionalEntityManager.update(
          Author,
          { id: author.id },
          { lastAssignedAt: new Date() },
        );

        // Step 1.1: get dailyUploadLimitRange
        let dailyUploadLimitRange = author.dailyUploadLimitRange;
        if (!dailyUploadLimitRange) {
          dailyUploadLimitRange =
            await this.settingService.getDailyUploadLimitRange();
        }

        // Step 2: Fetch new videos to reassign
        const nextFetchVideoId = author.nextFetchVideoId || 0;
        const videos = await transactionalEntityManager
          .createQueryBuilder(Video, 'video')
          .setLock('pessimistic_write')
          .where('video.author_id = :authorId', {
            authorId: author.id,
          })
          .andWhere('video.status In (:...status)', {
            status: eligibleVideoStatuses,
          })
          .andWhere('video.source_id::BIGINT >= :nextFetchVideoId', {
            nextFetchVideoId,
          })
          .orderBy('video.source_id::BIGINT')
          .limit(totalReassign)
          .select([
            'video.id',
            'video.sourceId',
            'video.description',
            'video.title',
            'video.cover',
            'video.video',
            'video.videoUrl',
            'video.videoDriveUrl',
          ])
          .getMany();

        if (!videos) {
          throw new NotFoundException('No tasks available');
        }

        const videoIds = videos.map((video) => video.id);

        await transactionalEntityManager.update(
          Video,
          { id: In(videoIds) },
          { status: VideoStatus.InProgress, assignedAt: new Date() },
        );

        // allocation.videoIds.push('0', ...videoIds);

        // await transactionalEntityManager.update(
        //   DailyChannelTrace,
        //   { id: allocation.id },
        //   { videoIds: allocation.videoIds },
        // );

        await transactionalEntityManager
          .createQueryBuilder()
          .update(DailyChannelTrace)
          .set({
            videoIds: () =>
              `array_cat(video_ids, ARRAY[:...newVideoIds]::text[])`, // Efficiently appends new IDs
          })
          .where('id = :id', { id: allocation.id })
          .setParameters({ newVideoIds: ['0', ...videoIds] }) // The array of new video IDs to append
          .execute();

        const uploadTimeConfig =
          await this.settingService.getUploadTimeConfig();
        result = { author, videos, dailyUploadLimitRange, uploadTimeConfig };
      },
    );

    return result;
  }

  async getNextTaskNoLimit(
    id: string,
  ): Promise<{ profileId: string; videos: Video[] }> {
    const eligibleVideoStatuses = [VideoStatus.InProgress, VideoStatus.Pending];

    let result = null;

    // Start a transaction and ensure all operations are within this transaction
    await this.videoRepository.manager.transaction(
      async (transactionalEntityManager) => {
        // Step 1: Select a random author
        const author = await transactionalEntityManager
          .createQueryBuilder(Author, 'author')
          .innerJoin(
            Video,
            'video',
            'author.id = video.authorId AND video.status IN (:...status)' +
              ' AND COALESCE("author"."next_fetch_video_id"::BIGINT, 0::BIGINT) <= "video"."source_id"::BIGINT',
            { status: eligibleVideoStatuses },
          )
          .select('author.id')
          .addSelect('author.nickname')
          .addSelect('author.uniqueId')
          .addSelect('author.sourceId')
          .addSelect('author.profileId')
          .addSelect('author.nextFetchVideoId')
          .addSelect('author.dailyUploadLimitRange')
          .addSelect('author.sourceKind')
          .andWhere('author.id = :id', { id })
          .andWhere('author.status = :authorStatus', {
            authorStatus: Status.Active,
          })
          .orderBy('author.priority', 'ASC', 'NULLS LAST')
          .limit(1)
          .getOne();

        if (!author) {
          throw new NotFoundException('No tasks available');
        }

        await transactionalEntityManager.update(
          Author,
          { id: author.id },
          { lastAssignedAt: new Date() },
        );

        // Step 1.1: get dailyUploadLimitRange
        let dailyUploadLimitRange = author.dailyUploadLimitRange;
        if (!dailyUploadLimitRange) {
          dailyUploadLimitRange =
            await this.settingService.getDailyUploadLimitRange();
        }

        // Step 2: Select a random video within the range [min, max]
        const randomRange = getRandomIntInclusive(
          dailyUploadLimitRange.min,
          dailyUploadLimitRange.max,
        );

        const nextFetchVideoId = author.nextFetchVideoId || 0;

        const videos = await transactionalEntityManager
          .createQueryBuilder(Video, 'video')
          .where('video.author_id = :authorId', {
            authorId: author.id,
          })
          .andWhere('video.status IN (:...status)', {
            status: eligibleVideoStatuses,
          })
          .andWhere('video.source_id::BIGINT >= :nextFetchVideoId', {
            nextFetchVideoId,
          })
          .orderBy('video.source_id::BIGINT')
          .limit(randomRange)
          .select([
            'video.id',
            'video.sourceId',
            'video.description',
            'video.title',
            'video.cover',
            'video.video',
            'video.videoUrl',
            'video.videoDriveUrl',
          ])
          .getMany();

        if (!videos) {
          throw new NotFoundException('No tasks available');
        }

        const videoIds = videos.map((video) => video.id);

        await transactionalEntityManager.update(
          Video,
          { id: In(videoIds) },
          { status: VideoStatus.InProgress, assignedAt: new Date() },
        );

        const uploadTimeConfig =
          await this.settingService.getUploadTimeConfig();
        result = { author, videos, dailyUploadLimitRange, uploadTimeConfig };
      },
    );

    return result;
  }
}
