import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DailyChannelTrace } from '../../entities';
import { DailyChannelTraceService } from './daily-channel-trace.service';
import { AdminDailyChannelTraceController } from './daily-channel-trace.controller.admin';
import { DailyTraceSummaryView } from '../../entities/view';
import { DailySummaryService } from './daily-summary.service';
import { AdminDailySummaryController } from './daily-summary.controller.admin';
import { LatestWarningDailyVideoView } from '../../entities/view/latest-warning-daily-video.view';
import { LatestWarningDailyVideoService } from './latest-warning-video.service';
import { AdminLatestWarningDailyVideoController } from './latest-warning-video.controller.admin';
// import { ClientDailyChannelTraceController } from './daily-channel-trace.controller.client';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DailyChannelTrace,
      DailyTraceSummaryView,
      LatestWarningDailyVideoView,
    ]),
  ],
  providers: [
    DailyChannelTraceService,
    DailySummaryService,
    LatestWarningDailyVideoService,
  ],
  controllers: [
    AdminDailyChannelTraceController,
    AdminDailySummaryController,
    AdminLatestWarningDailyVideoController,
    // ClientDailyChannelTraceController,
  ],
  exports: [
    DailyChannelTraceService,
    DailySummaryService,
    LatestWarningDailyVideoService,
  ],
})
export class DailyChannelTraceModule {}
