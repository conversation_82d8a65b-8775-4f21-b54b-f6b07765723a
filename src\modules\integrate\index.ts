const ALERT_CHANNEL = process.env.ALERT_CHANNEL || 'telegram';

import { sendMessage as larkSendMessage } from './lark';
import { sendMessage as telegramSendMessage } from './telegram';

const _handler = {
  lark: larkSendMessage,
  telegram: telegramSendMessage,
};

export async function sendMessage(message) {
  if (ALERT_CHANNEL == 'all') {
    return await Promise.allSettled(
      Object.values(_handler).map((func) => func(message)),
    );
  }
  if (!Object.keys(_handler).includes(ALERT_CHANNEL)) {
    throw Error(`Not support ${ALERT_CHANNEL}`);
  }
  return await _handler[ALERT_CHANNEL](message);
}
