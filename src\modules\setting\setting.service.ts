import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmCrudService } from '@nestjsx/crud-typeorm';

import { plainToInstance } from 'class-transformer';
import { DeepPartial, Repository } from 'typeorm';
import { SETTING } from '../../common/enum/setting.enum';
import { Setting } from '../../entities';
import { RangeDto } from '../../common/dto/range.dto';
import { Cacheable } from '@type-cacheable/core';
import { CacheTtlSeconds, RedisCacheService } from '../cache/cache.service';
import { CrudRequest } from '@nestjsx/crud';

const AUTHOR_COOLDOWN_PERIOD_DEFAULT = 10;
const AUTHOR_ROUND_BASE_V3_LIMIT_LAP_LIMIT = -1;
const AUTHOR_ROUND_BASE_V3_LIMIT_LAP_PARENT_CHILD_LIMIT = -1;
@Injectable()
export class SettingService
  extends TypeOrmCrudService<Setting>
  implements OnApplicationBootstrap
{
  private logger = new Logger(SettingService.name);

  constructor(
    @InjectRepository(Setting) public readonly repo: Repository<Setting>,
    private readonly _cacheService: RedisCacheService,
  ) {
    super(repo);
  }

  async onApplicationBootstrap() {
    this.logger.debug('onApplicationBootstrap');

    const defaultConfig = plainToInstance(Setting, [
      {
        key: SETTING.APP_DAILY_UPLOAD_LIMIT_RANGE,
        value: { max: 3, min: 1 },
        groupName: 'app',
        description: [
          `The dailyUploadLimitRange column defines the configurable range`,
          `for the number of videos that can be uploaded by a channel in a single day.`,
          `This range is used to randomly determine the actual daily upload limit for a channel`,
          `ensuring variability and flexibility in the system's scheduling process`,
        ].join(' '),
      },
      {
        key: SETTING.APP_UPLOAD_TIME_CONFIG,
        value: {
          primeTimeHours: { start: '18:00', end: '22:00' },
          normalTimeHours: { start: '7:00', end: '15:00' },
        },
        groupName: 'app',
        description: [
          `The uploadTimeConfig column defines configurable daily`,
          `upload limits for two distinct periods: prime time and normal time.`,
          `This feature allows the system to schedule video uploads dynamically`,
          `based on the time of day, ensuring optimal alignment with viewer`,
          `activity levels.`,
        ].join(' '),
      },
      {
        key: SETTING.AUTHOR_COOLDOWN_PERIOD,
        value: { minute: AUTHOR_COOLDOWN_PERIOD_DEFAULT },
        groupName: 'author',
        description: `The minimum time an author must wait before being eligible for a new assignment.`,
      },
      {
        key: SETTING.AUTHOR_ROUND_BASE_V3_LIMIT_LAP,
        value: { limit: AUTHOR_ROUND_BASE_V3_LIMIT_LAP_LIMIT },
        groupName: 'author',
        description: `The minimum time an author must wait before being eligible for a new assignment.`,
      },
      {
        key: SETTING.AUTHOR_ROUND_BASE_V3_LIMIT_LAP_PARENT_CHILD,
        value: { limit: AUTHOR_ROUND_BASE_V3_LIMIT_LAP_PARENT_CHILD_LIMIT },
        groupName: 'author',
        description: `The maximum number of laps per day for parent-child flow.`,
      },
    ]);

    await this.repo
      .createQueryBuilder()
      .insert()
      .into(Setting)
      .values(defaultConfig)
      .orIgnore()
      .execute();
  }

  async updateOne(
    req: CrudRequest,
    dto: DeepPartial<Setting>,
  ): Promise<Setting> {
    await this._cacheService.clearCacheByPattern('app:setting:*');
    return super.updateOne(req, dto);
  }

  // TODO: cache setting config
  @Cacheable({
    ttlSeconds: CacheTtlSeconds.ONE_HOUR,
    cacheKey: (args) => `app:setting:${args[0]}`,
  })
  async getByKey(key: string) {
    return this.repo.createQueryBuilder().andWhere({ key }).getOne();
  }

  async getDailyUploadLimitRange(): Promise<RangeDto> {
    const config = await this.getByKey(SETTING.APP_DAILY_UPLOAD_LIMIT_RANGE);
    return config.value;
  }

  async getUploadTimeConfig(): Promise<RangeDto> {
    const config = await this.getByKey(SETTING.APP_UPLOAD_TIME_CONFIG);
    return config.value;
  }

  async getAuthorCooldownPeriod(): Promise<number> {
    const config = await this.getByKey(SETTING.AUTHOR_COOLDOWN_PERIOD);
    return Number(config?.value?.minute) || AUTHOR_COOLDOWN_PERIOD_DEFAULT;
  }

  async getAuthorRoundBaseV3LimitLap(): Promise<number> {
    const config = await this.getByKey(SETTING.AUTHOR_ROUND_BASE_V3_LIMIT_LAP);
    return Number(config?.value?.limit) || AUTHOR_ROUND_BASE_V3_LIMIT_LAP_LIMIT;
  }

  async getAuthorRoundBaseV3LimitLapParentChild(): Promise<number> {
    const config = await this.getByKey(
      SETTING.AUTHOR_ROUND_BASE_V3_LIMIT_LAP_PARENT_CHILD,
    );
    return (
      Number(config?.value?.limit) ||
      AUTHOR_ROUND_BASE_V3_LIMIT_LAP_PARENT_CHILD_LIMIT
    );
  }
}
