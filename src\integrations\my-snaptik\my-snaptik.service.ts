import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { plainToInstance } from 'class-transformer';
import { getRandomIntInclusive, sleep } from '../../common/utils';
import { detectLanguage } from '../../common/utils/language';
import { retry } from '../../common/utils/retry.util';
import { SnapTikResponse } from './snaptik.dto';
import { UserInfo } from './user-info.dto';

const MY_SNAP_TIK_API_KEY = process.env.MY_SNAP_TIK_API_KEY;
const MY_SNAP_TIK_URL = process.env.MY_SNAP_TIK_URL;

@Injectable()
export class MySnapTikIntegrationService {
  private logger = new Logger(MySnapTikIntegrationService.name);

  private readonly baseUrl = MY_SNAP_TIK_URL || 'http://localhost:8080/api';

  private retryConfig = {
    attempts: 3,
    delayMs: 5_000,
    exponential: true,
  };

  private _getBaseHeader() {
    return {
      baseURL: this.baseUrl,
      headers: {
        'x-api-key': MY_SNAP_TIK_API_KEY,
      },
    };
  }

  async fetchSecUid(uniqueId: string): Promise<string> {
    const result = await retry(
      async () => {
        const response = await axios.get<UserInfo>(`/users/${uniqueId}`, {
          ...this._getBaseHeader(),
        });
        // this.logger.debug(
        //   `[fetchSecUid] ${uniqueId} => ${JSON.stringify(response.data)}`,
        // );

        return plainToInstance(UserInfo, response.data);
      },
      this.retryConfig.attempts,
      this.retryConfig.delayMs,
      this.retryConfig.exponential,
    );
    if (!result.secUid) {
      throw new Error(`No secUid found for uniqueId: ${uniqueId}`);
    }
    return result.secUid;
  }

  async fetchVideos(
    secUid: string,
    cursor?: string,
    msToken?: string,
  ): Promise<SnapTikResponse> {
    const params = { cursor, msToken };

    // this.logger.debug(
    //   `[fetchVideos] secUid: ${secUid}, cursor: ${cursor}, msToken: ${msToken}`,
    // );

    const result = await retry(
      async () => {
        const response = await axios.get<SnapTikResponse>(
          `/videos/sec/${secUid}`,
          {
            params,
            ...this._getBaseHeader(),
          },
        );

        return plainToInstance(SnapTikResponse, response.data);
      },
      this.retryConfig.attempts,
      this.retryConfig.delayMs,
      this.retryConfig.exponential,
    );
    return result;
  }

  async fetchAllVideos(uniqueId: string): Promise<SnapTikResponse[]> {
    let cursor: string | null = null;
    let hasMore = true;
    let msToken: string | null = null;
    const results: SnapTikResponse[] = [];

    const secUid = await this.fetchSecUid(uniqueId);

    while (hasMore) {
      // this.logger.debug(`Fetch Video ${uniqueId} ${cursor}`);
      const response = await this.fetchVideos(secUid, cursor, msToken);

      await sleep(getRandomIntInclusive(50, 200));

      // Store the current batch of results
      results.push(response);

      // Update cursor and hasMore
      cursor = response.cursor || null;
      hasMore = response.hasMore && !!cursor;
      msToken = response.msToken || null;
    }

    return results;
  }

  async *fetchAllVideosIterator(
    uniqueId: string,
    stopId = '',
  ): AsyncGenerator<SnapTikResponse> {
    let cursor: string | null = null;
    let hasMore = true;
    let msToken: string | null = null;

    const secUid = await this.fetchSecUid(uniqueId);

    while (hasMore) {
      try {
        this.logger.debug(`Fetch Video ${uniqueId} ${cursor}`);
        const response = await this.fetchVideos(secUid, cursor, msToken);

        await sleep(getRandomIntInclusive(50, 100));

        if (
          stopId &&
          response.items &&
          response.items.some((video) => video.videoId === stopId)
        ) {
          this.logger.debug(`Stop ID ${stopId} found. Stopping fetch.`);
          yield response;
          break;
        }

        yield response;

        // Update cursor and hasMore
        cursor = response.cursor || null;
        hasMore = response.hasMore && !!cursor;
        msToken = response.msToken || null;
      } catch (err) {
        this.logger.error(
          `Fetch failed for ${uniqueId} at cursor ${cursor}:`,
          err,
        );
        return;
      }
    }
  }

  /**
   ** Language Requirement: The author primarily creates content in English (lang = 'en').
   ** Video Count Requirement: The author has published at least 100 videos.
   * @param uniqueId
   * @returns
   */
  async isAuthorQualified(
    uniqueId: string,
    amountVideo?: number,
  ): Promise<boolean> {
    this.logger.warn(`Check author ${uniqueId} qualified`);
    const LANGUAGE = 'en';
    const MINIMUM_VIDEO_COUNT = amountVideo || 100;

    let cursor: string | null = null;
    let hasMore = true;
    const titles: string[] = [];
    const secUid = await this.fetchSecUid(uniqueId);

    while (hasMore) {
      this.logger.debug(`Fetch Video ${uniqueId} ${cursor}`);
      const response = await this.fetchVideos(secUid, cursor);

      await sleep(getRandomIntInclusive(50, 200));

      // Store the current batch of results
      titles.push(...response.items.map((item) => item.title));

      if (titles.length >= MINIMUM_VIDEO_COUNT) {
        break;
      }

      // Update cursor and hasMore
      cursor = response.cursor || null;
      hasMore = response.hasMore && !!cursor;
    }

    return (
      titles.length >= MINIMUM_VIDEO_COUNT &&
      detectLanguage(titles.filter((x) => !!x)) == LANGUAGE
    );
  }
}
