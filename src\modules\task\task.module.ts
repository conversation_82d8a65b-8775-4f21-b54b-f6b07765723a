import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Author, Video } from '../../entities';
import { DailyChannelTraceModule } from '../daily-channel-trace/daily-channel-trace.module';
import { SettingModule } from '../setting/setting.module';
// import { AdminTaskController } from './task.controller.admin';
import { RoundLapTrackingModule } from '../round-lap-tracking/round-lap-tracking.module';
import { ClientTaskController } from './task.controller.client';
import { TaskService } from './task.service';
import { TaskStatsService } from './task.service.stats';
import { ClientTaskControllerV2 } from './v2/task.controller.client';
import { TaskServiceV2 } from './v2/task.service';
import { ClientTaskControllerV3 } from './v3/task.controller.client';
import { TaskServiceV3 } from './v3/task.service';
import { TaskChildService } from './v3/task-child.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Video, Author]),
    SettingModule,
    DailyChannelTraceModule,
    RoundLapTrackingModule,
  ],
  providers: [
    TaskService,
    TaskStatsService,
    TaskServiceV2,
    TaskServiceV3,
    TaskChildService,
  ],
  controllers: [
    // AdminTaskController,
    ClientTaskController,
    // ClientTaskControllerV2,
    ClientTaskControllerV3,
  ],
})
export class TaskModule {}
